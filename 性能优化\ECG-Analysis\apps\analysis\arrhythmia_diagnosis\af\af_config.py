"""
房颤诊断配置文件
"""

# CWT小波变换参数
CWT_CONFIG = {
    'p_wave_f_min': 5,  # P波最小频率 (Hz)
    'p_wave_f_max': 15, # P波最大频率 (Hz)
    'f_wave_f_min': 4,  # F波最小频率 (Hz)
    'f_wave_f_max': 9,  # F波最大频率 (Hz)
    'scales': 50,       # 小波尺度数量
}

# 相量变换参数
PHASOR_CONFIG = {
    'filter_low': 1,    # 带通滤波低频截止 (Hz)
    'filter_high': 15,  # 带通滤波高频截止 (Hz)
    'pr_min': 0.12,     # PR最短间期 (秒)
    'pr_max': 0.22,     # PR最长间期 (秒)
    'p_duration': 0.10, # P波持续时间 (秒)
}

# 房颤诊断权重配置
AF_WEIGHTS = {
    'rp_diff': 0.30,   # P波缺失指标权重
    'rr_std': 0.15,    # RR间期标准差权重
    'rr_cv': 0.15,     # 变异系数权重
    'rr_iqr': 0.10,    # 四分位数范围权重
    'rr_mean': 0.10,   # 平均RR间期权重
    'f_wave': 0.20     # F波特征权重
}

# 阈值配置
THRESHOLDS = {
    'af_score': 0.65,    # 房颤评分阈值
    'rp_diff': 8.0,      # P波缺失指标归一化参数
    'rr_std': 0.15,      # RR间期标准差归一化参数
    'rr_cv': 0.15,       # 变异系数归一化参数
    'rr_iqr': 0.06,      # 四分位数范围归一化参数
    'rr_mean_range': 0.5 # RR平均值归一化范围
}

# 导联特定配置 (根据I导II导调整)
LEAD_SPECIFIC = {
    'I': {
        'af_score': 0.58,        # I导房颤评分阈值
        'rp_diff_weight': 0.30,  # I导P波缺失权重
        'rr_std_weight': 0.20,   # I导RR标准差权重
        'rr_cv_weight': 0.18,    # I导变异系数权重
        'f_wave_weight': 0.25,   # I导F波特征权重
        'phasor_enabled': True,  # 启用相量变换增强P波检测
        # I导特定阈值
        'thresholds': {
            'rp_diff': 5.0,      # I导P波缺失指标阈值降低
            'rr_std': 0.13,      # I导RR标准差阈值降低
        },
        'rp_diff_min_score': 0.3
    },
    'II': {
        'af_score': 0.68,        # II导房颤评分阈值
        'f_wave_weight': 0.25,   # II导F波特征权重增强
        'phasor_enabled': False, # II导不需要额外P波增强
    }
}

# 质量评估参数
QUALITY_CONFIG = {
    'min_quality': 0.4,          # 最低可接受质量分数
    'quality_threshold_factor': 0.2,  # 质量评分阈值调整因子
    'low_quality_weight_adjust': 0.2,  # 低质量时权重调整幅度
}

# 自相关分析参数
AUTOCORR_CONFIG = {
    'lag': 1,                    # 默认滞后窗口大小
    'min_intervals': 10,         # 最少需要的RR间期数量
    'af_threshold': 0.4,         # 房颤自相关阈值
}

# 双重评分策略参数
DUAL_SCORE_CONFIG = {
    'enabled': True,             # 是否启用双重评分
    'legacy_weight': 0.5,        # 传统算法权重
    'enhanced_weight': 0.5,      # 增强算法权重
    'minimal_legacy_score': 0.6,  # 传统算法最小分数比例
}

# 房颤诊断级别
AF_LEVELS = {
    'NONE': 0,      # 无房颤
    'POSSIBLE': 1,  # 可能房颤
    'LIKELY': 2,    # 很可能房颤
    'DEFINITE': 3   # 确定房颤
}

# 房颤诊断级别阈值
AF_LEVEL_THRESHOLDS = {
    'POSSIBLE': 0.5,   # 可能房颤阈值
    'LIKELY': 0.7,     # 很可能房颤阈值
    'DEFINITE': 0.85   # 确定房颤阈值
}

# 房颤检测参数
AF_PARAMS = {
    'min_duration': 5.0,    # 最短房颤持续时间(秒)
    'min_beats': 8,         # 最少心搏数
    'window_size': 10.0,    # 滑动窗口大小(秒)
    'step_size': 2.0,       # 滑动窗口步长(秒)
    'overlap': 0.8          # 窗口重叠率
}

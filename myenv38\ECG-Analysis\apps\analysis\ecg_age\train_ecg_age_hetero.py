import os
import glob
import math
import json
import random
import datetime
from typing import List, Tuple

import numpy as np
import h5py
from scipy import signal
import tensorflow as tf
from tensorflow.keras import layers, Model, callbacks, regularizers

# Enable GPU memory growth to avoid upfront full allocation
try:
    gpus = tf.config.list_physical_devices('GPU')
    for _g in gpus:
        tf.config.experimental.set_memory_growth(_g, True)
except Exception:
    pass


# ---------------------------
# Utils: Learning rate schedule
# ---------------------------
class WarmupCosineDecay(tf.keras.optimizers.schedules.LearningRateSchedule):
    def __init__(self, initial_learning_rate, decay_steps, warmup_steps=1000, alpha=1e-6):
        super().__init__()
        self.initial_learning_rate = float(initial_learning_rate)
        self.decay_steps = int(decay_steps)
        self.warmup_steps = int(warmup_steps)
        self.alpha = float(alpha)

    def __call__(self, step):
        step = tf.cast(step, tf.float32)
        warmup_steps = tf.cast(self.warmup_steps, tf.float32)
        init_lr = tf.cast(self.initial_learning_rate, tf.float32)

        warmup_lr = init_lr * (step / tf.maximum(1.0, warmup_steps))
        cosine_decay = tf.keras.optimizers.schedules.CosineDecay(
            initial_learning_rate=self.initial_learning_rate,
            decay_steps=max(1, self.decay_steps - self.warmup_steps),
            alpha=self.alpha,
        )(tf.cast(tf.maximum(step - warmup_steps, 0.0), tf.int64))
        cosine_decay = tf.cast(cosine_decay, tf.float32)
        return tf.where(step < warmup_steps, warmup_lr, cosine_decay)


# ---------------------------
# InceptionTime backbone (1D)
# ---------------------------

def inception_module(x, filters=64, kernel_sizes=(9, 19, 39), bottleneck=16, use_bias=False, l2=1e-4):
    if x.shape[-1] > 1:
        x_in = layers.Conv1D(bottleneck, 1, padding='same', use_bias=use_bias,
                             kernel_regularizer=regularizers.l2(l2))(x)
        x_in = layers.BatchNormalization()(x_in)
        x_in = layers.Activation('relu')(x_in)
    else:
        x_in = x

    convs = []
    for k in kernel_sizes:
        c = layers.Conv1D(filters, k, padding='same', use_bias=use_bias,
                          kernel_regularizer=regularizers.l2(l2))(x_in)
        c = layers.BatchNormalization()(c)
        c = layers.Activation('relu')(c)
        convs.append(c)

    pool = layers.MaxPooling1D(3, strides=1, padding='same')(x)
    pool = layers.Conv1D(filters, 1, padding='same', use_bias=use_bias,
                         kernel_regularizer=regularizers.l2(l2))(pool)
    pool = layers.BatchNormalization()(pool)
    pool = layers.Activation('relu')(pool)

    x = layers.Concatenate()(convs + [pool])
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)
    return x


def inception_block(x, filters=64, residual=True, **kwargs):
    shortcut = x
    x = inception_module(x, filters=filters, **kwargs)
    x = inception_module(x, filters=filters, **kwargs)
    x = inception_module(x, filters=filters, **kwargs)
    if residual:
        sc = layers.Conv1D(x.shape[-1], 1, padding='same')(shortcut)
        sc = layers.BatchNormalization()(sc)
        x = layers.Add()([x, sc])
        x = layers.Activation('relu')(x)
    return x


# ---------------------------
# Heteroscedastic regression head
# ---------------------------

def build_model(input_length=5000, l2=1e-4):
    inp = layers.Input(shape=(input_length, 1))
    x = layers.Conv1D(32, 9, strides=1, padding='same', kernel_regularizer=regularizers.l2(l2))(inp)
    x = layers.BatchNormalization()(x)
    x = layers.Activation('relu')(x)
    x = layers.MaxPooling1D(2)(x)

    x = inception_block(x, filters=32)
    x = layers.MaxPooling1D(2)(x)
    x = inception_block(x, filters=64)
    x = layers.MaxPooling1D(2)(x)
    x = inception_block(x, filters=128)

    # Multi-scale pooling and simple stats
    gap = layers.GlobalAveragePooling1D()(x)
    gmp = layers.GlobalMaxPooling1D()(x)
    stdp = layers.Lambda(lambda t: tf.math.reduce_std(t, axis=1))(x)
    feat = layers.Concatenate()([gap, gmp, stdp])

    feat = layers.Dense(128, activation='relu', kernel_regularizer=regularizers.l2(l2))(feat)
    feat = layers.Dropout(0.3)(feat)
    feat = layers.Dense(64, activation='relu', kernel_regularizer=regularizers.l2(l2))(feat)
    feat = layers.Dropout(0.2)(feat)

    # Predict mean and log-variance
    out2 = layers.Dense(2, name='mu_logvar')(feat)
    mu = layers.Lambda(lambda t: t[:, :1], name='mu')(out2)
    log_var = layers.Lambda(lambda t: t[:, 1:], name='log_var')(out2)

    # Training model outputs both for custom loss
    train_model = Model(inputs=inp, outputs=layers.Concatenate(name='train_out')([mu, log_var]))

    # Serving model outputs clipped mu only (cast to float32 for serving stability)
    clipped_mu = layers.Lambda(lambda t: tf.clip_by_value(t, 18.0, 80.0))(mu)
    clipped_mu = layers.Lambda(lambda t: tf.cast(t, tf.float32), name='age')(clipped_mu)
    serve_model = Model(inputs=inp, outputs=clipped_mu)
    return train_model, serve_model


def heteroscedastic_nll(y_true, y_pred):
    y_true = tf.cast(y_true, tf.float32)
    mu = tf.cast(y_pred[:, :1], tf.float32)
    log_var = tf.cast(y_pred[:, 1:], tf.float32)
    inv_var = tf.exp(-log_var)
    nll = 0.5 * (log_var + tf.square(y_true - mu) * inv_var)
    return tf.reduce_mean(nll)


def mae_mu(y_true, y_pred):
    y_true = tf.cast(y_true, tf.float32)
    mu = tf.cast(y_pred[:, :1], tf.float32)
    return tf.reduce_mean(tf.abs(y_true - mu))


def rmse_mu(y_true, y_pred):
    y_true = tf.cast(y_true, tf.float32)
    mu = tf.cast(y_pred[:, :1], tf.float32)
    return tf.sqrt(tf.reduce_mean(tf.square(y_true - mu)))


# ---------------------------
# Data generator with augmentation
# ---------------------------
class ECGDataGenerator(tf.keras.utils.Sequence):
    def __init__(self, file_paths: List[str], batch_size=64, is_training=True, target_length=5000,
                 age_min=18.0, age_max=80.0, cache_size=3, augment_prob=0.8, mixup_alpha=0.2):
        self.file_paths = file_paths
        self.batch_size = batch_size
        self.is_training = is_training
        self.target_length = target_length
        self.age_min = age_min
        self.age_max = age_max
        self.cache_size = cache_size
        self.augment_prob = augment_prob
        self.mixup_alpha = mixup_alpha

        # index mapping (file_idx, sample_idx)
        self.file_indices: List[Tuple[int, int]] = []
        for fidx, fp in enumerate(self.file_paths):
            try:
                with h5py.File(fp, 'r') as f:
                    ages = f['ages'][:]
                    valid = np.where((ages >= self.age_min) & (ages <= self.age_max))[0]
                    self.file_indices.extend([(fidx, int(i)) for i in valid])
            except Exception:
                pass

        self.indices = np.arange(len(self.file_indices))
        if self.is_training:
            np.random.shuffle(self.indices)

        self._cache = {}
        self._cache_order = []

    def __len__(self):
        return math.ceil(len(self.indices) / self.batch_size)

    def on_epoch_end(self):
        if self.is_training:
            np.random.shuffle(self.indices)

    # --- augment ops ---
    def _random_crop_or_pad(self, x: np.ndarray):
        L = len(x)
        T = self.target_length
        if L > T:
            if self.is_training:
                s = np.random.randint(0, L - T + 1)
            else:
                s = (L - T) // 2
            return x[s:s + T]
        elif L < T:
            pad = T - L
            left = pad // 2
            right = pad - left
            return np.pad(x, (left, right), mode='reflect')
        return x

    def _amplitude_scale(self, x):
        scale = np.random.uniform(0.8, 1.2)
        return x * scale

    def _gaussian_noise(self, x):
        std = np.std(x)
        noise = np.random.normal(0, 0.01 * (std + 1e-6), size=x.shape)
        return x + noise

    def _time_warp(self, x):
        # small speed change 0.9~1.1 via resample
        factor = np.random.uniform(0.9, 1.1)
        L = len(x)
        new_L = max(8, int(L * factor))
        x2 = signal.resample(x, new_L)
        return self._random_crop_or_pad(x2)

    def _random_mask(self, x):
        # mask a short segment
        L = len(x)
        seg = np.random.randint(int(0.01 * L), int(0.05 * L))
        s = np.random.randint(0, max(1, L - seg))
        x2 = x.copy()
        x2[s:s + seg] = 0
        return x2

    def _maybe_augment(self, x):
        if not self.is_training:
            return x
        ops = [self._amplitude_scale, self._gaussian_noise, self._time_warp, self._random_mask]
        # apply 1-2 ops randomly
        n_ops = np.random.choice([1, 2])
        for op in np.random.choice(ops, n_ops, replace=False):
            if np.random.rand() < self.augment_prob:
                x = op(x)
        return x

    def _standardize(self, x):
        m = np.mean(x)
        s = np.std(x)
        if s < 1e-6:
            return x - m
        return (x - m) / s

    def _get_handle(self, file_idx):
        if file_idx in self._cache:
            # move to end
            self._cache_order.remove(file_idx)
            self._cache_order.append(file_idx)
            return self._cache[file_idx]
        # open new
        if len(self._cache) >= self.cache_size:
            old = self._cache_order.pop(0)
            try:
                self._cache[old].close()
            except Exception:
                pass
            self._cache.pop(old, None)
        fh = h5py.File(self.file_paths[file_idx], 'r')
        self._cache[file_idx] = fh
        self._cache_order.append(file_idx)
        return fh

    def __getitem__(self, idx):
        batch_idx = self.indices[idx * self.batch_size: (idx + 1) * self.batch_size]
        xs = []
        ys = []

        # Open per-batch file handles to avoid stale/invalid handles in threads
        unique_fidx = sorted(set(self.file_indices[i][0] for i in batch_idx))
        file_handles = {}
        for fidx in unique_fidx:
            try:
                file_handles[fidx] = h5py.File(self.file_paths[fidx], 'r')
            except Exception:
                file_handles[fidx] = None

        try:
            for i in batch_idx:
                fidx, sidx = self.file_indices[i]
                f = file_handles.get(fidx)
                if f is None:
                    xs.append(np.zeros(self.target_length, dtype=np.float32))
                    ys.append(50.0)
                    continue
                try:
                    # Robust dataset access
                    x_ds = f['ecg_data'] if 'ecg_data' in f else None
                    y_ds = f['ages'] if 'ages' in f else None
                    if x_ds is None:
                        # try alternative common names
                        for k in ['data', 'signals', 'ecg', 'X']:
                            if k in f:
                                x_ds = f[k]
                                break
                    if y_ds is None:
                        for k in ['age', 'y', 'labels', 'target']:
                            if k in f:
                                y_ds = f[k]
                                break
                    if x_ds is None or y_ds is None:
                        raise KeyError('missing datasets')

                    x = x_ds[sidx]
                    y = float(y_ds[sidx])
                except Exception:
                    xs.append(np.zeros(self.target_length, dtype=np.float32))
                    ys.append(50.0)
                    continue

                x = np.asarray(x, dtype=np.float32)
                x = np.clip(x, -10, 10)
                x = self._random_crop_or_pad(x)
                if self.is_training:
                    x = self._maybe_augment(x)
                x = self._standardize(x)
                xs.append(x)
                ys.append(np.clip(y, self.age_min, self.age_max))
        finally:
            for fh in file_handles.values():
                try:
                    if fh is not None:
                        fh.close()
                except Exception:
                    pass

        X = np.expand_dims(np.array(xs, dtype=np.float32), axis=-1)  # (B, T, 1)
        y = np.array(ys, dtype=np.float32).reshape(-1, 1)

        # mixup at batch level
        if self.is_training and self.mixup_alpha > 0 and len(X) > 1 and np.random.rand() < 0.5:
            lam = np.random.beta(self.mixup_alpha, self.mixup_alpha)
            idx_perm = np.random.permutation(len(X))
            X = lam * X + (1 - lam) * X[idx_perm]
            y = lam * y + (1 - lam) * y[idx_perm]

        return X, y

    def close(self):
        for fh in list(self._cache.values()):
            try:
                fh.close()
            except Exception:
                pass
        self._cache.clear()
        self._cache_order.clear()


# ---------------------------
# Training pipeline
# ---------------------------

def make_callbacks(model_dir, save_format='keras'):
    os.makedirs(model_dir, exist_ok=True)
    best_name = 'best.keras' if save_format == 'keras' else 'best.h5'
    cbs = [
        callbacks.EarlyStopping(monitor='val_mae_mu', patience=12, restore_best_weights=True, min_delta=0.05, verbose=1),
        callbacks.ModelCheckpoint(os.path.join(model_dir, best_name), monitor='val_mae_mu', save_best_only=True),
        callbacks.CSVLogger(os.path.join(model_dir, 'train_log.csv')),
        callbacks.TensorBoard(log_dir=os.path.join(model_dir, 'tb')),
    ]
    return cbs


def train(data_dir: str, epochs=30, batch_size=32, input_length=5000, model_root='./models_ecg_age', augment_prob=0.8, mixup_alpha=0.2, save_format='keras', mixed_precision=False, xla_jit=False):
    files = glob.glob(os.path.join(data_dir, '*.h5')) + glob.glob(os.path.join(data_dir, '*.hdf5'))
    if len(files) == 0:
        raise FileNotFoundError(f'No HDF5 files found in {data_dir}')

    random.seed(42)
    random.shuffle(files)
    split = int(0.8 * len(files))
    train_files = files[:split]
    val_files = files[split:]

    train_gen = ECGDataGenerator(train_files, batch_size=batch_size, is_training=True, target_length=input_length, augment_prob=augment_prob, mixup_alpha=mixup_alpha)
    val_gen = ECGDataGenerator(val_files, batch_size=batch_size, is_training=False, target_length=input_length, augment_prob=0.0, mixup_alpha=0.0)

    steps_per_epoch = len(train_gen)
    total_steps = max(1, steps_per_epoch * epochs)


    # Optional accelerations
    if mixed_precision:
        from tensorflow.keras import mixed_precision as mx
        policy = mx.Policy('mixed_float16')
        mx.set_global_policy(policy)
    if xla_jit:
        tf.config.optimizer.set_jit(True)

    train_model, serve_model = build_model(input_length=input_length)

    lr_sched = WarmupCosineDecay(initial_learning_rate=2e-3, decay_steps=total_steps, warmup_steps=max(1000, total_steps // 20))
    opt = tf.keras.optimizers.Adam(learning_rate=lr_sched)

    train_model.compile(optimizer=opt, loss=heteroscedastic_nll, metrics=[mae_mu, rmse_mu])

    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    model_dir = os.path.join(model_root, f'ecg_age_hetero_{timestamp}')
    cbs = make_callbacks(model_dir, save_format=save_format)

    try:
        history = train_model.fit(train_gen, validation_data=val_gen, epochs=epochs, callbacks=cbs, workers=0, use_multiprocessing=False, verbose=1)
    finally:
        train_gen.close(); val_gen.close()

    # Save training model and serving model
    try:
        if save_format == 'keras':
            train_model.save(os.path.join(model_dir, 'train_model.keras'))
            serve_model.save(os.path.join(model_dir, 'serve_model.keras'))
        else:
            train_model.save(os.path.join(model_dir, 'train_model.h5'))
            serve_model.save(os.path.join(model_dir, 'serve_model.h5'))
    except Exception as e:
        # Fallback to H5 if .keras not supported
        train_model.save(os.path.join(model_dir, 'train_model.h5'))
        serve_model.save(os.path.join(model_dir, 'serve_model.h5'))

    # Export SavedModel for TF Serving under versioned folder
    serving_export_dir = os.path.join(model_dir, 'tf_serving', 'ecg_age', '1')
    os.makedirs(serving_export_dir, exist_ok=True)
    tf.saved_model.save(serve_model, serving_export_dir)

    # Save simple config for record
    with open(os.path.join(model_dir, 'config.json'), 'w') as f:
        json.dump({'input_length': input_length, 'note': 'Serving model outputs clipped mu in [18,80]'}, f, indent=2)

    print(f'Finished. Model saved at: {model_dir}\nTF Serving export: {serving_export_dir}')
    return model_dir


if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--data_dir', type=str, default='/opt/jupyter_notebook_workspace/data/TrainData/ecg_lead_i', help='Directory with *.h5/*.hdf5 files containing ecg_data and ages datasets')
    parser.add_argument('--epochs', type=int, default=30)
    parser.add_argument('--batch_size', type=int, default=32)
    parser.add_argument('--input_length', type=int, default=5000)
    parser.add_argument('--model_root', type=str, default='./models_ecg_age')
    parser.add_argument('--augment_prob', type=float, default=0.8)
    parser.add_argument('--mixup_alpha', type=float, default=0.2)
    parser.add_argument('--save_format', type=str, default='h5', choices=['keras', 'h5'])
    parser.add_argument('--mixed_precision', action='store_true')
    parser.add_argument('--xla_jit', action='store_true')
    parser.set_defaults(mixed_precision=True, xla_jit=False)
    args, unknown = parser.parse_known_args()

    train(data_dir=args.data_dir,
          epochs=args.epochs,
          batch_size=args.batch_size,
          input_length=args.input_length,
          model_root=args.model_root,
          augment_prob=args.augment_prob,
          mixup_alpha=args.mixup_alpha,
          save_format=args.save_format,
          mixed_precision=args.mixed_precision,
          xla_jit=args.xla_jit)



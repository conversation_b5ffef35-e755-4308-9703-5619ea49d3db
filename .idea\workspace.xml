<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8d1212f7-a660-486b-9c37-1052ad45cd81" name="更改" comment="调整心房颤动诊断逻辑">
      <change afterPath="$PROJECT_DIR$/myenv38/ECG-Analysis/apps/api/tests/api_test.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Dockerfile" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ECG-Analysis-Minute/apps/analysis/arrhythmia_diagnosis/af/af_detector.py" beforeDir="false" afterPath="$PROJECT_DIR$/ECG-Analysis-Minute/apps/analysis/arrhythmia_diagnosis/af/af_detector.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ECG-Analysis-Minute/apps/analysis/health_metrics/rf_model_index.py" beforeDir="false" afterPath="$PROJECT_DIR$/ECG-Analysis-Minute/apps/analysis/health_metrics/rf_model_index.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ECG-Analysis-Minute/apps/models/analysis_models.py" beforeDir="false" afterPath="$PROJECT_DIR$/ECG-Analysis-Minute/apps/models/analysis_models.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ECG-Analysis-Minute/apps/signal_analysis/available.py" beforeDir="false" afterPath="$PROJECT_DIR$/ECG-Analysis-Minute/apps/signal_analysis/available.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Shanghai" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/af/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/af/af_config.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/af/af_detector.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/af/af_signal_processing.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/af/af_thresholds.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/af/af_utils.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/diagnosis.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/diagnosis_detail/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/diagnosis_detail/af_detail.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/diagnosis_detail/pvc_detail.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/pvc.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/sna.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/snb.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/arrhythmia_diagnosis/snt.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/cad_cardiomyopathy/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/cad_cardiomyopathy/diagnosis.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/custom_exception.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/data_filter.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/detect_wave.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/ecg_llm_processing.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/ecg_signal_processing.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/multiple_model/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/multiple_model/conclusion_diagnostic.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/multiple_model/model/tf_model_multi_label.keras" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/noise_model/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/noise_model/model/noise_model.h5" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/common/noise_model/noise_recognition.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/diagnosis_filter/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/diagnosis_filter/filter.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/diagnosis_filter/filter_rules.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/ecg_age/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/ecg_age/config_reader.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/ecg_age/diagnosis.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/ecg_age/model/1209ecg_model.h5" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/ecg_age/model/1227ecg_model.h5" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/ecg_age/model/cnn_ecg_model_age.h5" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/ecg_age/model/cnn_ecg_model_age_1113.h5" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/ecg_age/model/tf_ecg_model_cnn_1125.h5" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/ecg_age/personalized_age_config.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/ecg_age/personalized_prediction.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/gravity/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/gravity/business.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/gravity/state_analysis.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/health_metrics/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/health_metrics/diagnosis.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/health_metrics/model/best_multioutput_model.pkl" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/health_metrics/rf_model_index.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/pqrstc/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/pqrstc/diagnosis.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/analysis/whitelist.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/api/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/api/diagnose/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/api/diagnose/business.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/api/diagnose/interface.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/api/login.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/api/urls.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/models/Interface_log_models.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/models/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/models/analysis_models.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/models/base_model.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/models/ecg_analysis_modes.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/signal_analysis/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/signal_analysis/available.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/signal_analysis/filtering.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/signal_analysis/waveform.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/utils/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/utils/api_analysis_log.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/utils/decorator.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/utils/get_response.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/utils/logger_helper.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/utils/param_extraction.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/utils/redis_helper.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/craw_pro/browser_crawler.py" beforeDir="false" afterPath="$PROJECT_DIR$/craw_pro/browser_crawler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docker-compose.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ecg/ECG-DynamicReport/apps/utils/ai_chat.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecg/ECG-DynamicReport/apps/utils/ai_chat.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecg/ECG-DynamicReport/apps/utils/elasticsearch.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecg/ECG-DynamicReport/apps/utils/elasticsearch.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecg/ECG-DynamicReport/apps/utils/qiniu_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecg/ECG-DynamicReport/apps/utils/qiniu_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecg/ECG-DynamicReport/apps/utils/sql_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecg/ECG-DynamicReport/apps/utils/sql_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ecganalysis-sync/final_review_gate.py" beforeDir="false" afterPath="$PROJECT_DIR$/ecganalysis-sync/final_review_gate.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/global_settings.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/myenv38/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af/af_detector.py" beforeDir="false" afterPath="$PROJECT_DIR$/myenv38/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af/af_detector.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/myenv38/ECG-Analysis/apps/analysis/health_metrics/diagnosis.py" beforeDir="false" afterPath="$PROJECT_DIR$/myenv38/ECG-Analysis/apps/analysis/health_metrics/diagnosis.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/myenv38/ECG-Analysis/apps/analysis/health_metrics/rf_model_index.py" beforeDir="false" afterPath="$PROJECT_DIR$/myenv38/ECG-Analysis/apps/analysis/health_metrics/rf_model_index.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/requirements.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af/af_detector.py" beforeDir="false" afterPath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af/af_detector.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/diagnosis.py" beforeDir="false" afterPath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/diagnosis.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/analysis/health_metrics/rf_model_index.py" beforeDir="false" afterPath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/analysis/health_metrics/rf_model_index.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/api/diagnose/interface.py" beforeDir="false" afterPath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/api/diagnose/interface.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/signal_analysis/available.py" beforeDir="false" afterPath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/signal_analysis/available.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/signal_analysis/filtering.py" beforeDir="false" afterPath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/signal_analysis/filtering.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/signal_analysis/waveform.py" beforeDir="false" afterPath="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/signal_analysis/waveform.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/Dockerfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/__init__.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af/af_config.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af/af_config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af/af_signal_processing.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af/af_signal_processing.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/af_module.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/diagnosis.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/diagnosis.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/pac.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/pac.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/pvc.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/pvc.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/common/multiple_model/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/common/multiple_model/conclusion_diagnostic.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/common/multiple_model/model/tf_model_multi_label.keras" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/common/noise_model/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/common/noise_model/model/noise_model.h5" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/common/noise_model/noise_recognition.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/ecg_age/config_reader.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/ecg_age/diagnosis.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/ecg_age/diagnosis.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/ecg_age/personalized_age_config.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/ecg_age/personalized_prediction.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/health_metrics/rf_model_index.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/health_metrics/rf_model_index.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/api/diagnose/interface.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/api/diagnose/interface.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/signal_analysis/available.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/signal_analysis/available.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/signal_analysis/filtering.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/signal_analysis/filtering.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/signal_analysis/waveform.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/signal_analysis/waveform.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/utils/get_response.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/utils/get_response.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/docker-compose.yml" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/docker-compose.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/global_settings.py" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/global_settings.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/性能优化/ECG-Analysis/requirements.txt" beforeDir="false" afterPath="$PROJECT_DIR$/性能优化/ECG-Analysis/requirements.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DjangoConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform))&#10;import django; print('Django %s' % django.get_version())&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;if 'setup' in dir(django): django.setup()&#10;import django_manage_shell; django_manage_shell.run(PROJECT_ROOT)">
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform))&#10;import django; print('Django %s' % django.get_version())&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;if 'setup' in dir(django): django.setup()&#10;import django_manage_shell; django_manage_shell.run(PROJECT_ROOT)" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo&#10;locals().update(ScriptInfo(create_app=None).load_app().make_shell_context())&#10;print(&quot;Python %s on %s\nApp: %s [%s]\nInstance: %s&quot; % (sys.version, sys.platform, app.import_name, app.env, app.instance_path))" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Rebase.Settings">
    <option name="NEW_BASE" value="origin/dev" />
  </component>
  <component name="Git.Settings">
    <excluded-from-favorite>
      <branch-storage>
        <map>
          <entry type="LOCAL">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$/性能优化/ECG-Analysis" source="master" />
                <branch-info repo="$PROJECT_DIR$/myenv38/ECG-Analysis" source="master" />
              </list>
            </value>
          </entry>
          <entry type="REMOTE">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$/myenv38/ECG-Analysis" source="origin/master" />
                <branch-info repo="$PROJECT_DIR$/性能优化/ECG-Analysis" source="origin/master" />
                <branch-info repo="$PROJECT_DIR$/ecg/ECG-Analysis" source="origin/master" />
                <branch-info repo="$PROJECT_DIR$/SoloShuttlePose" source="origin/main" />
                <branch-info repo="$PROJECT_DIR$/TrackNet-Badminton-Tracking-tensorflow2" source="origin/main" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </excluded-from-favorite>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/性能优化/ECG-Analysis" value="6cbce9af026e222b33a5b2f7753312572b852561" />
      </map>
    </option>
    <option name="RECENT_COMMON_BRANCH" value="dev" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/噪音优化/ECG-Analysis" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/Documents/WeChat Files/wxid_6y5obyazlq2222/FileStorage/File/2024-10/diagnosis.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/ECGAnalysis/apps/analysis/ecg_age/diagnosis.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/pythonProject/sql_helper.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/api/tests/api_test(1).py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/心电图绘制代码.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/性能优化/ECG-Analysis/start_server_auto_restart.ps1" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="DEPENDENCY_CHECKER_PROBLEMS_TAB" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2o3QWFVw6msCRD6fqJGbTQv2mqr" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Django 服务器.Python.executor": "Run",
    "Notification.DisplayName-DoNotAsk-Database detector": "数据库检测器",
    "Notification.DoNotAsk-Database detector": "true",
    "PowerShell.start_server_auto_restart.ps1.executor": "Run",
    "Python 测试.pytest (api_test.py 内).executor": "Run",
    "Python 测试.pytest (test_janus.py 内).executor": "Run",
    "Python.02.25image_recognition.executor": "Run",
    "Python.1.23 医院数据汇总查询.executor": "Run",
    "Python.1.8 hdf5格式文件转.executor": "Run",
    "Python.10.12心脏年龄预测.executor": "Run",
    "Python.10.24深度学习代码验证.executor": "Run",
    "Python.10.29肖总预测数据.executor": "Run",
    "Python.11.03深度学习模型F1指标计算.executor": "Run",
    "Python.11.04心脏年龄模型F1指标计算.executor": "Run",
    "Python.11.06心脏年龄模型构建.executor": "Run",
    "Python.11.08CNN构建心脏年龄模型.executor": "Run",
    "Python.11.08CNN模型加载.executor": "Run",
    "Python.11.08CNN预测.executor": "Run",
    "Python.11.11csv文件合并.executor": "Run",
    "Python.11.13CNN模型使用.executor": "Run",
    "Python.11.13CNN模型更新.executor": "Run",
    "Python.11.16模型结构查看.executor": "Run",
    "Python.11.18 Resnet模型构建.executor": "Run",
    "Python.11.19 Resnet模型使用.executor": "Run",
    "Python.11.20模型使用.executor": "Run",
    "Python.11.21年龄分布图像.executor": "Run",
    "Python.11.25高俊懿.executor": "Run",
    "Python.11.26 数据库链接.executor": "Run",
    "Python.12.10 房颤检测单独的模块.executor": "Run",
    "Python.12.11_SNA_test.executor": "Run",
    "Python.12.30房颤诊断逻辑实现.executor": "Run",
    "Python.1224ECGAge模型调用代码.executor": "Run",
    "Python.1224年龄可视化.executor": "Run",
    "Python.9.08心脏年龄预测.executor": "Run",
    "Python.CSV文件合并.executor": "Run",
    "Python.Step_1_QA.executor": "Run",
    "Python.TrackNet.executor": "Run",
    "Python.af_detail.executor": "Run",
    "Python.api_test(1).executor": "Run",
    "Python.api_test.executor": "Run",
    "Python.api_tester.executor": "Run",
    "Python.available.executor": "Run",
    "Python.badminton_highlight.executor": "Run",
    "Python.browser_crawler.executor": "Run",
    "Python.business.executor": "Debug",
    "Python.crawler.executor": "Run",
    "Python.db_operations.executor": "Run",
    "Python.diagnosis.executor": "Run",
    "Python.ecg_diagnosis_processing.executor": "Run",
    "Python.ecg_plot_10sec.executor": "Run",
    "Python.global_settings.executor": "Run",
    "Python.hea和dat文件处理.executor": "Run",
    "Python.interface.executor": "Run",
    "Python.main.executor": "Run",
    "Python.match_ecg_results.executor": "Run",
    "Python.pac.executor": "Run",
    "Python.personalized_prediction.executor": "Run",
    "Python.pvc.executor": "Run",
    "Python.pvc_detail.executor": "Run",
    "Python.python predict.py --&lt;args&gt;.executor": "Run",
    "Python.scratch.executor": "Run",
    "Python.train_wesad_stress.executor": "Run",
    "Python.usage_example.executor": "Run",
    "Python.video2img.executor": "Run",
    "Python.waveform.executor": "Run",
    "Python.代码下载.executor": "Run",
    "Python.作图.executor": "Run",
    "Python.信息检索系统.executor": "Run",
    "Python.图片类PDF提取.executor": "Run",
    "Python.在线模型.executor": "Run",
    "Python.处理表格.executor": "Run",
    "Python.心电图绘制代码.executor": "Run",
    "Python.房颤验证指标计算.executor": "Run",
    "Python.模型文件.executor": "Run",
    "Python.测试.executor": "Run",
    "Python.预测.executor": "Run",
    "RunOnceActivity.OpenDjangoStructureViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.pycharm.django.structure.promotion.once.per.project": "true",
    "git-widget-placeholder": "噪音优化-ruzj",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/Project/myenv38/ECG-Analysis/apps/analysis/health_metrics/model",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "configurable.group.tools",
    "two.files.diff.last.used.file": "D:/Project/myenv38/ECG-Analysis/apps/api/tests/api_test.py",
    "two.files.diff.last.used.folder": "D:/Project/myenv38",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "ChangesTree.GroupingKeys": [
      "directory",
      "repository"
    ],
    "com.intellij.ide.scratch.ScratchImplUtil$2/新建临时文件": [
      "JupyterPython",
      "Python"
    ]
  }
}]]></component>
  <component name="PyConsoleOptionsProvider">
    <option name="myPythonConsoleState">
      <console-settings module-name="Python" is-module-sdk="true">
        <option name="myUseModuleSdk" value="true" />
        <option name="myModuleName" value="Python" />
      </console-settings>
    </option>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Project\myenv38\ECG-Analysis\apps\analysis\health_metrics\model" />
      <recent name="D:\Project\ECG-Analysis-Minute" />
      <recent name="D:\Project\噪音优化\ECG-Analysis" />
      <recent name="D:\Project\噪音优化\ECG-Analysis\apps\api" />
      <recent name="D:\Project\ECGAnalysis\apps\api\tests" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Project\data_frame" />
      <recent name="D:\Project\checkpoints" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-PY-242.22855.92" />
        <option value="bundled-python-sdk-b068d85d1acf-399fe30bd8c1-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-242.22855.92" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="8d1212f7-a660-486b-9c37-1052ad45cd81" name="更改" comment="" />
      <created>1730095808883</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1730095808883</updated>
      <workItem from="1730095809925" duration="858000" />
      <workItem from="1730097021340" duration="2938000" />
      <workItem from="1730100303019" duration="669000" />
      <workItem from="1730104509461" duration="3273000" />
      <workItem from="1730170008830" duration="2297000" />
      <workItem from="1730186475230" duration="601000" />
      <workItem from="1730190798645" duration="8727000" />
      <workItem from="1730200781917" duration="2501000" />
      <workItem from="1730252082083" duration="3612000" />
      <workItem from="1730266344691" duration="259000" />
      <workItem from="1730274386131" duration="599000" />
      <workItem from="1730339795373" duration="524000" />
      <workItem from="1730340759162" duration="1142000" />
      <workItem from="1730342236271" duration="2814000" />
      <workItem from="1730345114553" duration="1468000" />
      <workItem from="1730355330334" duration="8154000" />
      <workItem from="1730373474812" duration="3858000" />
      <workItem from="1730378206624" duration="1344000" />
      <workItem from="1730379730604" duration="115000" />
      <workItem from="1730424775254" duration="11388000" />
      <workItem from="1730439314646" duration="6607000" />
      <workItem from="1730450068981" duration="5678000" />
      <workItem from="1730458808075" duration="295000" />
      <workItem from="1730459844570" duration="1912000" />
      <workItem from="1730461779824" duration="6250000" />
      <workItem from="1730468420236" duration="11000" />
      <workItem from="1730622708288" duration="5863000" />
      <workItem from="1730688657143" duration="7357000" />
      <workItem from="1730716612929" duration="1000" />
      <workItem from="1730782408241" duration="5746000" />
      <workItem from="1730872681108" duration="1000" />
      <workItem from="1730884128131" duration="14112000" />
      <workItem from="1730905049879" duration="1000" />
      <workItem from="1730907255045" duration="5000" />
      <workItem from="1730907455780" duration="25000" />
      <workItem from="1730908590353" duration="5956000" />
      <workItem from="1730943390260" duration="26959000" />
      <workItem from="1730994420119" duration="2769000" />
      <workItem from="1731030453034" duration="24765000" />
      <workItem from="1731071574387" duration="94000" />
      <workItem from="1731226850257" duration="9975000" />
      <workItem from="1731295845682" duration="8654000" />
      <workItem from="1731340605100" duration="2000" />
      <workItem from="1731340685755" duration="637000" />
      <workItem from="1731380884193" duration="2831000" />
      <workItem from="1731462380767" duration="5000" />
      <workItem from="1731464229452" duration="16000" />
      <workItem from="1731465971180" duration="3301000" />
      <workItem from="1731479017679" duration="16000" />
      <workItem from="1731486328965" duration="2285000" />
      <workItem from="1731509858500" duration="257000" />
      <workItem from="1731562370753" duration="1000" />
      <workItem from="1731569426508" duration="3411000" />
      <workItem from="1731574034875" duration="43000" />
      <workItem from="1731574201275" duration="1000" />
      <workItem from="1731575419909" duration="1000" />
      <workItem from="1731578242768" duration="6000" />
      <workItem from="1731578806886" duration="1000" />
      <workItem from="1731650897384" duration="1000" />
      <workItem from="1731652931711" duration="3000" />
      <workItem from="1731653602898" duration="3000" />
      <workItem from="1731688913018" duration="332000" />
      <workItem from="1731851438251" duration="1207000" />
      <workItem from="1731909132080" duration="676000" />
      <workItem from="1731918673658" duration="630000" />
      <workItem from="1731926239676" duration="599000" />
      <workItem from="1731934050882" duration="1000" />
      <workItem from="1731934331573" duration="2000" />
      <workItem from="1731984464370" duration="1000" />
      <workItem from="1731994659886" duration="50000" />
      <workItem from="1732002375494" duration="3000" />
      <workItem from="1732034420641" duration="1074000" />
      <workItem from="1732084078386" duration="18000" />
      <workItem from="1732085229201" duration="16050000" />
      <workItem from="1732109179029" duration="1290000" />
      <workItem from="1732158702177" duration="1492000" />
      <workItem from="1732164945481" duration="7287000" />
      <workItem from="1732185745126" duration="3577000" />
      <workItem from="1732200857718" duration="1005000" />
      <workItem from="1732245098049" duration="16567000" />
      <workItem from="1732352976940" duration="2315000" />
      <workItem from="1732368244412" duration="925000" />
      <workItem from="1732372531172" duration="1104000" />
      <workItem from="1732420860237" duration="3181000" />
      <workItem from="1732439979206" duration="636000" />
      <workItem from="1732510238745" duration="5186000" />
      <workItem from="1732516896518" duration="327000" />
      <workItem from="1732521715547" duration="477000" />
      <workItem from="1732522745303" duration="134000" />
      <workItem from="1732585411144" duration="6659000" />
      <workItem from="1732594773828" duration="331000" />
      <workItem from="1732605874366" duration="10817000" />
      <workItem from="1732674860979" duration="5164000" />
      <workItem from="1732686325611" duration="1713000" />
      <workItem from="1732699786013" duration="14138000" />
      <workItem from="1732719738122" duration="5401000" />
      <workItem from="1732757462447" duration="11027000" />
      <workItem from="1732775543749" duration="1990000" />
      <workItem from="1732778799153" duration="65000" />
      <workItem from="1732780095707" duration="5479000" />
      <workItem from="1732791244058" duration="1513000" />
      <workItem from="1732795270498" duration="8994000" />
      <workItem from="1732808721537" duration="6015000" />
      <workItem from="1732842218428" duration="20608000" />
      <workItem from="1733030311603" duration="7000" />
      <workItem from="1733107438758" duration="2031000" />
      <workItem from="1733194712958" duration="6275000" />
      <workItem from="1733205488315" duration="2488000" />
      <workItem from="1733291258842" duration="6645000" />
      <workItem from="1733369672330" duration="714000" />
      <workItem from="1733377461942" duration="1640000" />
      <workItem from="1733380105936" duration="309000" />
      <workItem from="1733387527115" duration="4930000" />
      <workItem from="1733393944179" duration="1110000" />
      <workItem from="1733404951999" duration="195000" />
      <workItem from="1733405177903" duration="1927000" />
      <workItem from="1733446615909" duration="23393000" />
      <workItem from="1733713113018" duration="31747000" />
      <workItem from="1733810627405" duration="14840000" />
      <workItem from="1733888019784" duration="7141000" />
      <workItem from="1733910967184" duration="735000" />
      <workItem from="1733984060010" duration="3747000" />
      <workItem from="1734071867601" duration="450000" />
      <workItem from="1734339326494" duration="1885000" />
      <workItem from="1734343598520" duration="3344000" />
      <workItem from="1734404166087" duration="18921000" />
      <workItem from="1734486226069" duration="23670000" />
      <workItem from="1734517462737" duration="9569000" />
      <workItem from="1734538153483" duration="666000" />
      <workItem from="1734577745537" duration="6941000" />
      <workItem from="1734675426053" duration="2470000" />
      <workItem from="1734922240614" duration="9608000" />
      <workItem from="1734937204928" duration="17932000" />
      <workItem from="1734970666459" duration="1782000" />
      <workItem from="1735009353916" duration="7007000" />
      <workItem from="1735023807510" duration="3409000" />
      <workItem from="1735107509209" duration="766000" />
      <workItem from="1735108838460" duration="281000" />
      <workItem from="1735201144778" duration="10014000" />
      <workItem from="1735395714983" duration="862000" />
      <workItem from="1735489470363" duration="268000" />
      <workItem from="1735490677395" duration="20218000" />
      <workItem from="1735540830562" duration="20455000" />
      <workItem from="1735616766656" duration="1358000" />
      <workItem from="1735795325023" duration="9764000" />
      <workItem from="1735805736147" duration="890000" />
      <workItem from="1735834212643" duration="1252000" />
      <workItem from="1735872479097" duration="4534000" />
      <workItem from="1735877983716" duration="447000" />
      <workItem from="1735887292750" duration="796000" />
      <workItem from="1735888164513" duration="160000" />
      <workItem from="1735888480555" duration="1987000" />
      <workItem from="1736070198484" duration="17000" />
      <workItem from="1736091064027" duration="3571000" />
      <workItem from="1736130567869" duration="4025000" />
      <workItem from="1736156162047" duration="543000" />
      <workItem from="1736224712811" duration="1129000" />
      <workItem from="1736230502434" duration="965000" />
      <workItem from="1736238285336" duration="1289000" />
      <workItem from="1736317680634" duration="3943000" />
      <workItem from="1736335135941" duration="314000" />
      <workItem from="1736475112649" duration="2990000" />
      <workItem from="1736478179885" duration="47000" />
      <workItem from="1736746908058" duration="3366000" />
      <workItem from="1736765038843" duration="677000" />
      <workItem from="1736838340761" duration="1043000" />
      <workItem from="1736923301219" duration="1504000" />
      <workItem from="1736925508236" duration="1166000" />
      <workItem from="1737436116573" duration="366000" />
      <workItem from="1737526030312" duration="1648000" />
      <workItem from="1737531015377" duration="431000" />
      <workItem from="1737535055891" duration="824000" />
      <workItem from="1737613211197" duration="1099000" />
      <workItem from="1737620903753" duration="3395000" />
      <workItem from="1737723667984" duration="1581000" />
      <workItem from="1737873886573" duration="216000" />
      <workItem from="1737875572327" duration="2818000" />
      <workItem from="1737965982644" duration="1487000" />
      <workItem from="1738721031742" duration="8202000" />
      <workItem from="1738811512772" duration="5847000" />
      <workItem from="1738896945463" duration="12868000" />
      <workItem from="1738938474894" duration="2293000" />
      <workItem from="1738993401900" duration="9088000" />
      <workItem from="1739023284835" duration="43000" />
      <workItem from="1739173927006" duration="4183000" />
      <workItem from="1739206824182" duration="27000" />
      <workItem from="1739256132084" duration="8291000" />
      <workItem from="1739266225646" duration="7725000" />
      <workItem from="1739329151296" duration="7265000" />
      <workItem from="1739432635191" duration="11020000" />
      <workItem from="1739461884600" duration="21000" />
      <workItem from="1739462072396" duration="1084000" />
      <workItem from="1739463435216" duration="158000" />
      <workItem from="1739463664355" duration="23000" />
      <workItem from="1739463749825" duration="1431000" />
      <workItem from="1739498482920" duration="2260000" />
      <workItem from="1739526829682" duration="760000" />
      <workItem from="1739777119028" duration="6402000" />
      <workItem from="1739791825179" duration="85000" />
      <workItem from="1739792853891" duration="6046000" />
      <workItem from="1739874576723" duration="152000" />
      <workItem from="1739954445300" duration="6710000" />
      <workItem from="1740027998699" duration="3182000" />
      <workItem from="1740033588661" duration="5346000" />
      <workItem from="1740050770174" duration="1237000" />
      <workItem from="1740067716163" duration="5011000" />
      <workItem from="1740108957823" duration="10108000" />
      <workItem from="1740124348605" duration="1962000" />
      <workItem from="1740128068177" duration="519000" />
      <workItem from="1740132730390" duration="999000" />
      <workItem from="1740144361859" duration="415000" />
      <workItem from="1740386070326" duration="104000" />
      <workItem from="1740455036659" duration="3608000" />
      <workItem from="1740462949685" duration="524000" />
      <workItem from="1740463486624" duration="8110000" />
      <workItem from="1740546781546" duration="3892000" />
      <workItem from="1740550701959" duration="1841000" />
      <workItem from="1740556478810" duration="1063000" />
      <workItem from="1740635760473" duration="1066000" />
      <workItem from="1740636884139" duration="21196000" />
      <workItem from="1740705911894" duration="21359000" />
      <workItem from="1740756613390" duration="1494000" />
      <workItem from="1740805904762" duration="14839000" />
      <workItem from="1740904578881" duration="361000" />
      <workItem from="1740985501884" duration="2719000" />
      <workItem from="1741245831535" duration="6106000" />
      <workItem from="1741252705586" duration="246000" />
      <workItem from="1741333237851" duration="1048000" />
      <workItem from="1741598082297" duration="1074000" />
      <workItem from="1741661963583" duration="3858000" />
      <workItem from="1741759053995" duration="4197000" />
      <workItem from="1741763282520" duration="8399000" />
      <workItem from="1741847470418" duration="1415000" />
      <workItem from="1741853433789" duration="4834000" />
      <workItem from="1741880489420" duration="106000" />
      <workItem from="1741936320018" duration="36000" />
      <workItem from="1742195174149" duration="6464000" />
      <workItem from="1742276127449" duration="5485000" />
      <workItem from="1742288021632" duration="5621000" />
      <workItem from="1742293858697" duration="2616000" />
      <workItem from="1742354403065" duration="5284000" />
      <workItem from="1742369969566" duration="2050000" />
      <workItem from="1742457845809" duration="9140000" />
      <workItem from="1742549915849" duration="1610000" />
      <workItem from="1742868687451" duration="20459000" />
      <workItem from="1742918508199" duration="2421000" />
      <workItem from="1742951107381" duration="20506000" />
      <workItem from="1742991762795" duration="714000" />
      <workItem from="1743058981482" duration="1945000" />
      <workItem from="1743061170632" duration="1367000" />
      <workItem from="1743062554601" duration="5902000" />
      <workItem from="1743309666447" duration="487000" />
      <workItem from="1743389138402" duration="5510000" />
      <workItem from="1743406989566" duration="6921000" />
      <workItem from="1743488352018" duration="16567000" />
      <workItem from="1743520058115" duration="1416000" />
      <workItem from="1743573538829" duration="2896000" />
      <workItem from="1743579262307" duration="318000" />
      <workItem from="1743583620754" duration="626000" />
      <workItem from="1743668829350" duration="3650000" />
      <workItem from="1744031620064" duration="1438000" />
      <workItem from="1744096655392" duration="2131000" />
      <workItem from="1744167293380" duration="3214000" />
      <workItem from="1744180481276" duration="10169000" />
      <workItem from="1744268096221" duration="6544000" />
      <workItem from="1744275846418" duration="5041000" />
      <workItem from="1744343018641" duration="149000" />
      <workItem from="1744355109445" duration="6516000" />
      <workItem from="1744594514890" duration="19050000" />
      <workItem from="1744623662065" duration="5680000" />
      <workItem from="1744782580052" duration="5930000" />
      <workItem from="1744791803625" duration="3666000" />
      <workItem from="1744860407793" duration="4469000" />
      <workItem from="1744869559615" duration="12477000" />
      <workItem from="1744938947433" duration="16807000" />
      <workItem from="1745211789636" duration="8675000" />
      <workItem from="1745230422747" duration="5230000" />
      <workItem from="1745296832580" duration="2165000" />
      <workItem from="1745302511491" duration="682000" />
      <workItem from="1745310979928" duration="3592000" />
      <workItem from="1745385668415" duration="7504000" />
      <workItem from="1745401654118" duration="8722000" />
      <workItem from="1745415561802" duration="345000" />
      <workItem from="1745461550371" duration="817000" />
      <workItem from="1745462674839" duration="3117000" />
      <workItem from="1745477382130" duration="3005000" />
      <workItem from="1745480419225" duration="5415000" />
      <workItem from="1745486051433" duration="6427000" />
      <workItem from="1745549483116" duration="13758000" />
      <workItem from="1745730835526" duration="15155000" />
      <workItem from="1745816824263" duration="17996000" />
      <workItem from="1745838027915" duration="6002000" />
      <workItem from="1745896881000" duration="616000" />
      <workItem from="1745903141298" duration="525000" />
      <workItem from="1745903733476" duration="486000" />
      <workItem from="1745910125746" duration="1686000" />
      <workItem from="1745917299218" duration="7802000" />
      <workItem from="1745977914822" duration="3566000" />
      <workItem from="1745998443253" duration="378000" />
      <workItem from="1745998827784" duration="2536000" />
      <workItem from="1746002114086" duration="2376000" />
      <workItem from="1746502851139" duration="6576000" />
      <workItem from="1746585349204" duration="4271000" />
      <workItem from="1746692614015" duration="264000" />
      <workItem from="1746692922741" duration="2846000" />
      <workItem from="1746697278132" duration="2922000" />
      <workItem from="1746756522818" duration="7053000" />
      <workItem from="1746773388765" duration="9264000" />
      <workItem from="1746799499129" duration="5860000" />
      <workItem from="1746855123334" duration="5789000" />
      <workItem from="1746946119944" duration="5080000" />
      <workItem from="1747028743772" duration="8133000" />
      <workItem from="1747057069298" duration="1323000" />
      <workItem from="1747099726576" duration="10262000" />
      <workItem from="1747127725302" duration="6617000" />
      <workItem from="1747192404884" duration="11439000" />
      <workItem from="1747286818002" duration="12266000" />
      <workItem from="1747308240435" duration="1603000" />
      <workItem from="1747366602643" duration="12640000" />
      <workItem from="1747622777738" duration="1819000" />
      <workItem from="1747801926203" duration="974000" />
      <workItem from="1747803113433" duration="931000" />
      <workItem from="1747900846817" duration="4153000" />
      <workItem from="1747909819486" duration="4829000" />
      <workItem from="1747978017590" duration="3537000" />
      <workItem from="1747984348821" duration="15739000" />
      <workItem from="1748228025806" duration="1386000" />
      <workItem from="1748238590156" duration="1219000" />
      <workItem from="1748258304227" duration="4464000" />
      <workItem from="1748327605782" duration="4448000" />
      <workItem from="1748334245495" duration="5149000" />
      <workItem from="1748411394838" duration="10656000" />
      <workItem from="1748426631841" duration="39000" />
      <workItem from="1748426685174" duration="104000" />
      <workItem from="1748426848161" duration="98000" />
      <workItem from="1748427035287" duration="2568000" />
      <workItem from="1748430370502" duration="1264000" />
      <workItem from="1748446292346" duration="3526000" />
      <workItem from="1748502167344" duration="6390000" />
      <workItem from="1748509955787" duration="1766000" />
      <workItem from="1748532379327" duration="1485000" />
      <workItem from="1748573794589" duration="791000" />
      <workItem from="1748581047959" duration="10174000" />
      <workItem from="1748612503954" duration="1232000" />
      <workItem from="1748931887889" duration="2845000" />
      <workItem from="1748941058314" duration="4070000" />
      <workItem from="1748959448912" duration="1407000" />
      <workItem from="1748961718480" duration="580000" />
      <workItem from="1749003825527" duration="18950000" />
      <workItem from="1749087257777" duration="263000" />
      <workItem from="1749114635839" duration="6598000" />
      <workItem from="1749178365819" duration="2301000" />
      <workItem from="1749199375542" duration="6435000" />
      <workItem from="1749451367453" duration="5099000" />
      <workItem from="1749463496120" duration="2381000" />
      <workItem from="1749532584563" duration="1443000" />
      <workItem from="1749534978749" duration="19367000" />
      <workItem from="1749598934771" duration="31112000" />
      <workItem from="1749683801350" duration="14514000" />
      <workItem from="1749722590452" duration="7337000" />
      <workItem from="1749733709368" duration="897000" />
      <workItem from="1749734627055" duration="2722000" />
      <workItem from="1749795901205" duration="1424000" />
      <workItem from="1750056191141" duration="4539000" />
      <workItem from="1750131806910" duration="17518000" />
      <workItem from="1750163102608" duration="592000" />
      <workItem from="1750610043381" duration="54000" />
      <workItem from="1750657979065" duration="1430000" />
      <workItem from="1750659969463" duration="3195000" />
      <workItem from="1750665307574" duration="4006000" />
      <workItem from="1750742400158" duration="5122000" />
      <workItem from="1750747968310" duration="7903000" />
      <workItem from="1750819728508" duration="2882000" />
      <workItem from="1750847644499" duration="8279000" />
      <workItem from="1750904281581" duration="4453000" />
      <workItem from="1750917612271" duration="12133000" />
      <workItem from="1750937171728" duration="55000" />
      <workItem from="1750946409362" duration="214000" />
      <workItem from="1750949624505" duration="111000" />
      <workItem from="1750990629329" duration="12258000" />
      <workItem from="1751250134296" duration="823000" />
      <workItem from="1751265131444" duration="3102000" />
      <workItem from="1751274492366" duration="4130000" />
      <workItem from="1751342775625" duration="3713000" />
      <workItem from="1751373775948" duration="7742000" />
      <workItem from="1751504781118" duration="38000" />
      <workItem from="1751530650287" duration="1416000" />
      <workItem from="1751551380053" duration="309000" />
      <workItem from="1751557370376" duration="1537000" />
      <workItem from="1751594659125" duration="3860000" />
      <workItem from="1751875307552" duration="458000" />
      <workItem from="1751877242357" duration="400000" />
      <workItem from="1751877651812" duration="453000" />
      <workItem from="1751878112722" duration="232000" />
      <workItem from="1751878622121" duration="1776000" />
      <workItem from="1751884562236" duration="1911000" />
      <workItem from="1751895368516" duration="7965000" />
      <workItem from="1751937959567" duration="5014000" />
      <workItem from="1751956719421" duration="4559000" />
      <workItem from="1751980375024" duration="311000" />
      <workItem from="1752027607803" duration="2705000" />
      <workItem from="1752155150396" duration="1522000" />
      <workItem from="1752203402923" duration="1199000" />
      <workItem from="1752220809347" duration="4046000" />
      <workItem from="1752228775302" duration="1144000" />
      <workItem from="1752460420984" duration="2850000" />
      <workItem from="1752465567230" duration="15143000" />
      <workItem from="1752544964262" duration="642000" />
      <workItem from="1752548983577" duration="3000" />
      <workItem from="1752638723224" duration="1601000" />
      <workItem from="1752641892194" duration="1139000" />
      <workItem from="1752644955806" duration="955000" />
      <workItem from="1752720343565" duration="1730000" />
      <workItem from="1752755960718" duration="468000" />
      <workItem from="1753094389815" duration="1460000" />
      <workItem from="1753158595087" duration="1193000" />
      <workItem from="1753163703537" duration="1295000" />
      <workItem from="1753165017492" duration="1620000" />
      <workItem from="1753166655958" duration="492000" />
      <workItem from="1753180480741" duration="3048000" />
      <workItem from="1753185872270" duration="3867000" />
      <workItem from="1753238872680" duration="572000" />
      <workItem from="1753249703012" duration="370000" />
      <workItem from="1753250380619" duration="663000" />
      <workItem from="1753251218719" duration="1314000" />
      <workItem from="1753253942653" duration="28000" />
      <workItem from="1753278556808" duration="561000" />
      <workItem from="1753283076793" duration="793000" />
      <workItem from="1753324843404" duration="209000" />
      <workItem from="1753337298106" duration="120000" />
      <workItem from="1753347726924" duration="192000" />
      <workItem from="1753410621366" duration="87000" />
      <workItem from="1753431759368" duration="143000" />
      <workItem from="1753431977790" duration="279000" />
      <workItem from="1753432487549" duration="167000" />
      <workItem from="1753674058720" duration="86000" />
      <workItem from="1753674165621" duration="339000" />
      <workItem from="1753674703577" duration="649000" />
      <workItem from="1753675362766" duration="289000" />
      <workItem from="1753683598072" duration="43000" />
      <workItem from="1753683711795" duration="149000" />
      <workItem from="1753702755943" duration="70000" />
      <workItem from="1753702860186" duration="164000" />
      <workItem from="1753705718288" duration="164000" />
      <workItem from="1753760557389" duration="99000" />
      <workItem from="1753760674371" duration="147000" />
      <workItem from="1753760841061" duration="107000" />
      <workItem from="1753769124651" duration="77000" />
      <workItem from="1753769484526" duration="1116000" />
      <workItem from="1753770933627" duration="103000" />
      <workItem from="1753779079935" duration="365000" />
      <workItem from="1753784818360" duration="380000" />
      <workItem from="1753844623446" duration="146000" />
      <workItem from="1753845214695" duration="46000" />
      <workItem from="1753845747237" duration="1650000" />
      <workItem from="1753871060733" duration="138000" />
      <workItem from="1753872161779" duration="90000" />
      <workItem from="1753941473931" duration="1000" />
      <workItem from="1753946393292" duration="404000" />
      <workItem from="1753955116643" duration="819000" />
      <workItem from="1754303180710" duration="182000" />
      <workItem from="1754355334619" duration="698000" />
      <workItem from="1754381425280" duration="241000" />
      <workItem from="1754481260064" duration="4698000" />
      <workItem from="1754488892054" duration="545000" />
      <workItem from="1754490307589" duration="41000" />
      <workItem from="1754542466793" duration="703000" />
      <workItem from="1754543315805" duration="647000" />
      <workItem from="1754624748199" duration="395000" />
      <workItem from="1754625205849" duration="372000" />
      <workItem from="1754633740475" duration="364000" />
      <workItem from="1754634116298" duration="349000" />
      <workItem from="1754634488166" duration="119000" />
      <workItem from="1754634625775" duration="369000" />
      <workItem from="1754635006602" duration="989000" />
      <workItem from="1754964422715" duration="3392000" />
      <workItem from="1755095675241" duration="1991000" />
      <workItem from="1755166240398" duration="2404000" />
    </task>
    <task id="LOCAL-00005" summary="更新了室性早搏的诊断逻辑">
      <option name="closed" value="true" />
      <created>1739508433710</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1739508433710</updated>
    </task>
    <task id="LOCAL-00006" summary="更新了心脏年龄个性化预测逻辑">
      <option name="closed" value="true" />
      <created>1739784579928</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1739784579928</updated>
    </task>
    <task id="LOCAL-00007" summary="更新了室性早搏并整合了数据预处理">
      <option name="closed" value="true" />
      <created>1740033482761</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1740033482761</updated>
    </task>
    <task id="LOCAL-00008" summary="优化了室性早搏中对噪音的处理">
      <option name="closed" value="true" />
      <created>1740127670957</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1740127670957</updated>
    </task>
    <task id="LOCAL-00009" summary="优化了室性早搏中对噪音的处理">
      <option name="closed" value="true" />
      <created>1740127765893</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1740127765893</updated>
    </task>
    <task id="LOCAL-00010" summary="更新了心脏年龄个性化预测逻辑">
      <option name="closed" value="true" />
      <created>1740128127428</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1740128127428</updated>
    </task>
    <task id="LOCAL-00011" summary="优化了室性早搏的诊断逻辑">
      <option name="closed" value="true" />
      <created>1741251507366</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1741251507366</updated>
    </task>
    <task id="LOCAL-00012" summary="噪音识别优化更新">
      <option name="closed" value="true" />
      <created>1742982143894</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1742982143894</updated>
    </task>
    <task id="LOCAL-00013" summary="噪音识别优化补充更新">
      <option name="closed" value="true" />
      <created>1742992022404</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1742992022404</updated>
    </task>
    <task id="LOCAL-00014" summary="噪音识别优化及PQRST值更新">
      <option name="closed" value="true" />
      <created>1743508476567</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1743508476567</updated>
    </task>
    <task id="LOCAL-00015" summary="后端返回问题修复更新">
      <option name="closed" value="true" />
      <created>1743521027393</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1743521027393</updated>
    </task>
    <task id="LOCAL-00016" summary="接口窦性心律，窦性心动过速过缓修复更新">
      <option name="closed" value="true" />
      <created>1743577893606</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1743577893606</updated>
    </task>
    <task id="LOCAL-00017" summary="更新噪声识别方法的注释">
      <option name="closed" value="true" />
      <created>1744032785592</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1744032785592</updated>
    </task>
    <task id="LOCAL-00018" summary="更新噪声识别方法的注释">
      <option name="closed" value="true" />
      <created>1744033030803</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1744033030803</updated>
    </task>
    <task id="LOCAL-00019" summary="症状诊断方法迁移">
      <option name="closed" value="true" />
      <created>1744269686132</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1744269686132</updated>
    </task>
    <task id="LOCAL-00020" summary="PVC诊断添加注释并删除冗余部分计算">
      <option name="closed" value="true" />
      <created>1744343101527</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1744343101527</updated>
    </task>
    <task id="LOCAL-00021" summary="心电图报告计算及返回字段添加">
      <option name="closed" value="true" />
      <created>1745395045691</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1745395045691</updated>
    </task>
    <task id="LOCAL-00022" summary="心电图报告计算及返回字段添加">
      <option name="closed" value="true" />
      <created>1745395227502</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1745395227502</updated>
    </task>
    <task id="LOCAL-00023" summary="接口返回字段添加">
      <option name="closed" value="true" />
      <created>1745395335548</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1745395335548</updated>
    </task>
    <task id="LOCAL-00024" summary="修改房颤诊断中变异系数的阈值，以匹配其他诊断">
      <option name="closed" value="true" />
      <created>1745461855560</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1745461855560</updated>
    </task>
    <task id="LOCAL-00025" summary="PVC映射补充">
      <option name="closed" value="true" />
      <created>1745473761803</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1745473761803</updated>
    </task>
    <task id="LOCAL-00026" summary="心电图报告错误修复">
      <option name="closed" value="true" />
      <created>1745484668746</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1745484668746</updated>
    </task>
    <task id="LOCAL-00027" summary="修正了四个指数输入维度和兼容问题">
      <option name="closed" value="true" />
      <created>1745486137266</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1745486137266</updated>
    </task>
    <task id="LOCAL-00028" summary="计算及返回错误修复">
      <option name="closed" value="true" />
      <created>1745489223290</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1745489223290</updated>
    </task>
    <task id="LOCAL-00029" summary="结论及诊断提示生成">
      <option name="closed" value="true" />
      <created>1745570872428</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1745570872428</updated>
    </task>
    <task id="LOCAL-00030" summary="窦性心律不齐诊断修复">
      <option name="closed" value="true" />
      <created>1745981912045</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1745981912045</updated>
    </task>
    <task id="LOCAL-00031" summary="窦性心律不齐诊断修复">
      <option name="closed" value="true" />
      <created>1745982300234</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1745982300234</updated>
    </task>
    <task id="LOCAL-00032" summary="窦性心律不齐诊断修复">
      <option name="closed" value="true" />
      <created>1745982418439</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1745982418439</updated>
    </task>
    <task id="LOCAL-00033" summary="房颤室早噪音优化">
      <option name="closed" value="true" />
      <created>1748428340222</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1748428340222</updated>
    </task>
    <task id="LOCAL-00034" summary="优化依赖更新">
      <option name="closed" value="true" />
      <created>1748430414612</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1748430414612</updated>
    </task>
    <task id="LOCAL-00035" summary="房颤诊断更改">
      <option name="closed" value="true" />
      <created>1748599415713</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1748599415713</updated>
    </task>
    <task id="LOCAL-00036" summary="房颤诊断更改">
      <option name="closed" value="true" />
      <created>1748599543007</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1748599543007</updated>
    </task>
    <task id="LOCAL-00037" summary="房颤诊断更改">
      <option name="closed" value="true" />
      <created>1748601266950</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1748601266950</updated>
    </task>
    <task id="LOCAL-00038" summary="修改房颤诊断字典错误问题">
      <option name="closed" value="true" />
      <created>1748613147984</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1748613147984</updated>
    </task>
    <task id="LOCAL-00039" summary="针对平直线和高频振荡噪声返回的更新">
      <option name="closed" value="true" />
      <created>1748960549089</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1748960549089</updated>
    </task>
    <task id="LOCAL-00040" summary="噪音识别代码整理">
      <option name="closed" value="true" />
      <created>1749091453848</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1749091453848</updated>
    </task>
    <task id="LOCAL-00041" summary="更新诊断的注释信息">
      <option name="closed" value="true" />
      <created>1749726845956</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1749726845956</updated>
    </task>
    <task id="LOCAL-00042" summary="更新运动状态噪音定义">
      <option name="closed" value="true" />
      <created>1750661291172</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1750661291172</updated>
    </task>
    <task id="LOCAL-00043" summary="同步">
      <option name="closed" value="true" />
      <created>1750785186799</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1750785186799</updated>
    </task>
    <task id="LOCAL-00044" summary="修复平直线和高频噪声问题">
      <option name="closed" value="true" />
      <created>1750935325982</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1750935325982</updated>
    </task>
    <task id="LOCAL-00045" summary="噪音代码bug修复">
      <option name="closed" value="true" />
      <created>1750937148935</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1750937148945</updated>
    </task>
    <task id="LOCAL-00046" summary="af诊断更改">
      <option name="closed" value="true" />
      <created>1751597681304</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1751597681304</updated>
    </task>
    <task id="LOCAL-00047" summary="af诊断更改">
      <option name="closed" value="true" />
      <created>1751597769962</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1751597769962</updated>
    </task>
    <task id="LOCAL-00048" summary="噪音识别调整">
      <option name="closed" value="true" />
      <created>1751882493370</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1751882493370</updated>
    </task>
    <task id="LOCAL-00049" summary="噪音识别调整增加interface">
      <option name="closed" value="true" />
      <created>1751938336260</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1751938336260</updated>
    </task>
    <task id="LOCAL-00050" summary="调整PVC的诊断逻辑和运行时长">
      <option name="closed" value="true" />
      <created>1752640922260</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1752640922260</updated>
    </task>
    <task id="LOCAL-00051" summary="调整PVC的诊断逻辑和运行时长">
      <option name="closed" value="true" />
      <created>1752643027837</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1752643027838</updated>
    </task>
    <task id="LOCAL-00052" summary="调整PVC的诊断逻辑和运行时长">
      <option name="closed" value="true" />
      <created>1752645798327</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1752645798328</updated>
    </task>
    <task id="LOCAL-00053" summary="调整心房颤动诊断逻辑">
      <option name="closed" value="true" />
      <created>1753872197825</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1753872197825</updated>
    </task>
    <option name="localTasksCounter" value="54" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="dev" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/dev" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="心电图报告计算及返回字段添加" />
    <MESSAGE value="接口返回字段添加" />
    <MESSAGE value="修改房颤诊断中变异系数的阈值，以匹配其他诊断" />
    <MESSAGE value="PVC映射补充" />
    <MESSAGE value="心电图报告错误修复" />
    <MESSAGE value="修正了四个指数输入维度和兼容问题" />
    <MESSAGE value="计算及返回错误修复" />
    <MESSAGE value="结论及诊断提示生成" />
    <MESSAGE value="窦性心律不齐诊断修复" />
    <MESSAGE value="房颤室早噪音优化" />
    <MESSAGE value="优化依赖更新" />
    <MESSAGE value="房颤诊断更改" />
    <MESSAGE value="修改房颤诊断字典错误问题" />
    <MESSAGE value="针对平直线和高频振荡噪声返回的更新" />
    <MESSAGE value="噪音识别代码整理" />
    <MESSAGE value="更新诊断的注释信息" />
    <MESSAGE value="更新运动状态噪音定义" />
    <MESSAGE value="同步" />
    <MESSAGE value="修复平直线和高频噪声问题" />
    <MESSAGE value="噪音代码bug修复" />
    <MESSAGE value="af诊断更改" />
    <MESSAGE value="噪音识别调整" />
    <MESSAGE value="噪音识别调整增加interface" />
    <MESSAGE value="调整PVC的诊断逻辑和运行时长" />
    <MESSAGE value="调整心房颤动诊断逻辑" />
    <option name="LAST_COMMIT_MESSAGE" value="调整心房颤动诊断逻辑" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/pythonProject/10.29肖总预测数据.py</url>
          <line>16</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/ECGAnalysis/apps/analysis/ecg_age/diagnosis.py</url>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/00001_hr_hea$api_tester.coverage" NAME="api_tester 覆盖结果" MODIFIED="1746800333904" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/api/tests" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$PDF.coverage" NAME="图片类PDF提取 覆盖结果" MODIFIED="1744272401585" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Python$10_29.coverage" NAME="10.29肖总预测数据 覆盖结果" MODIFIED="1730266591274" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$browser_crawler.coverage" NAME="browser_crawler 覆盖结果" MODIFIED="1752030696439" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/craw_pro" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$11_26_.coverage" NAME="11.26 数据库链接 覆盖结果" MODIFIED="1732605967903" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$.coverage" NAME="房颤验证指标计算 覆盖结果" MODIFIED="1754542673283" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$train_wesad_stress.coverage" NAME="train_wesad_stress 覆盖结果" MODIFIED="1755100311573" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/training" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$11_19_Resnet.coverage" NAME="11.19 Resnet模型使用 覆盖结果" MODIFIED="1732278013045" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$diagnosis.coverage" NAME="diagnosis 覆盖结果" MODIFIED="1753249745549" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myenv38/ECG-Analysis/apps/analysis/arrhythmia_diagnosis" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$11_25.coverage" NAME="11.25高俊懿 覆盖结果" MODIFIED="1732516961354" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$available.coverage" NAME="available 覆盖结果" MODIFIED="1751896453872" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myenv38/ECG-Analysis/apps/signal_analysis" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$CSV.coverage" NAME="CSV文件合并 覆盖结果" MODIFIED="1754967677002" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$interface.coverage" NAME="interface 覆盖结果" MODIFIED="1745218107478" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/api/diagnose" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$business.coverage" NAME="business 覆盖结果" MODIFIED="1752224178285" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/api/diagnose" />
    <SUITE FILE_PATH="coverage/Project$10_29.coverage" NAME="10.29肖总预测数据 覆盖结果" MODIFIED="1731041028596" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$python_predict_py____lt_args_gt_.coverage" NAME="python predict.py --&amp;lt;args&amp;gt; 覆盖结果" MODIFIED="1744796364237" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/TrackNet-Badminton-Tracking-tensorflow2" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$pytest__test_janus_py__.coverage" NAME="pytest (test_janus.py 内) 覆盖结果" MODIFIED="1740461718767" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/janus" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$main.coverage" NAME="main 覆盖结果" MODIFIED="1752027664390" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/craw_pro" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$12_10_.coverage" NAME="12.10 房颤检测单独的模块 覆盖结果" MODIFIED="1733823762667" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$1_23_.coverage" NAME="1.23 医院数据汇总查询 覆盖结果" MODIFIED="1737613258708" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/Project$11_08CNN.coverage" NAME="11.08CNN预测 覆盖结果" MODIFIED="1731302959048" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$1224.coverage" NAME="1224年龄可视化 覆盖结果" MODIFIED="1735026479836" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/Project$10_24.coverage" NAME="10.24深度学习代码验证 覆盖结果" MODIFIED="1731302774789" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/Project$11_13CNN.coverage" NAME="11.13CNN模型使用 覆盖结果" MODIFIED="1731509880977" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$1_8_hdf5.coverage" NAME="1.8 hdf5格式文件转 覆盖结果" MODIFIED="1736335448636" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$ecg_plot_10sec.coverage" NAME="ecg_plot_10sec 覆盖结果" MODIFIED="1742549958941" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Project$11_03F1.coverage" NAME="11.03深度学习模型F1指标计算 覆盖结果" MODIFIED="1730628506524" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$video2img.coverage" NAME="video2img 覆盖结果" MODIFIED="1744793757863" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/TrackNet-Badminton-Tracking-tensorflow2" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$11_21.coverage" NAME="11.21年龄分布图像 覆盖结果" MODIFIED="1732245766614" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/Project$api_test.coverage" NAME="api_test 覆盖结果" MODIFIED="1731478496048" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ECGAnalysis/apps/api/tests" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$ecg_diagnosis_processing.coverage" NAME="ecg_diagnosis_processing 覆盖结果" MODIFIED="1742199758503" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ECGAnalysis/apps/analysis/common" />
    <SUITE FILE_PATH="coverage/Project$scratch.coverage" NAME="scratch 覆盖结果" MODIFIED="1730441984602" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$APPLICATION_CONFIG_DIR$/scratches" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$11_20.coverage" NAME="11.20模型使用 覆盖结果" MODIFIED="1732353979958" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$11_03F1.coverage" NAME="11.03深度学习模型F1指标计算 覆盖结果" MODIFIED="1749032260900" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$api_test_1_.coverage" NAME="api_test(1) 覆盖结果" MODIFIED="1751958243602" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/api/tests" />
    <SUITE FILE_PATH="coverage/Project$11_04F1.coverage" NAME="11.04心脏年龄模型F1指标计算 覆盖结果" MODIFIED="1730691323345" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/Python$10_12.coverage" NAME="10.12心脏年龄预测 覆盖结果" MODIFIED="1730170030256" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/Python$9_08.coverage" NAME="9.08心脏年龄预测 覆盖结果" MODIFIED="1730252991653" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$global_settings.coverage" NAME="global_settings 覆盖结果" MODIFIED="1747127874330" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myenv38/ECG-Analysis" />
    <SUITE FILE_PATH="coverage/Project$11_11csv.coverage" NAME="11.11csv文件合并 覆盖结果" MODIFIED="1731298091219" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$crawler.coverage" NAME="crawler 覆盖结果" MODIFIED="1752027943190" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/craw_pro" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$12_11_SNA_test.coverage" NAME="12.11_SNA_test 覆盖结果" MODIFIED="1733888367849" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$1224ECGAge.coverage" NAME="1224ECGAge模型调用代码 覆盖结果" MODIFIED="1735024173821" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$badminton_highlight.coverage" NAME="badminton_highlight 覆盖结果" MODIFIED="1744796389002" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/yoloV5/badminton" />
    <SUITE FILE_PATH="coverage/Project$11_06.coverage" NAME="11.06心脏年龄模型构建 覆盖结果" MODIFIED="1731035056930" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$pac.coverage" NAME="pac 覆盖结果" MODIFIED="1749604797601" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$usage_example.coverage" NAME="usage_example 覆盖结果" MODIFIED="1753180820518" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ECG-Analysis-Minute/apps/analysis/health_metrics" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$pvc_detail.coverage" NAME="pvc_detail 覆盖结果" MODIFIED="1745487051693" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/diagnosis_detail" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$10_24.coverage" NAME="10.24深度学习代码验证 覆盖结果" MODIFIED="1732714690514" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$waveform.coverage" NAME="waveform 覆盖结果" MODIFIED="1748941402022" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/signal_analysis" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$02_25image_recognition.coverage" NAME="02.25image_recognition 覆盖结果" MODIFIED="1740469699347" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$Step_1_QA.coverage" NAME="Step_1_QA 覆盖结果" MODIFIED="1743074607914" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/MiniRAG/reproduce" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$af_detail.coverage" NAME="af_detail 覆盖结果" MODIFIED="1745487979146" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/arrhythmia_diagnosis/diagnosis_detail" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$personalized_prediction.coverage" NAME="personalized_prediction 覆盖结果" MODIFIED="1744186683141" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/analysis/ecg_age" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$match_ecg_results.coverage" NAME="match_ecg_results 覆盖结果" MODIFIED="1743575438496" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data_frame" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$pvc.coverage" NAME="pvc 覆盖结果" MODIFIED="1752469146599" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/myenv38/ECG-Analysis/apps/analysis/arrhythmia_diagnosis" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$12_30.coverage" NAME="12.30房颤诊断逻辑实现 覆盖结果" MODIFIED="1735552767509" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$TrackNet.coverage" NAME="TrackNet 覆盖结果" MODIFIED="1744793708000" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/TrackNet-Badminton-Tracking-tensorflow2" />
    <SUITE FILE_PATH="coverage/Project$9_08.coverage" NAME="9.08心脏年龄预测 覆盖结果" MODIFIED="1730913180604" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$pytest__api_test_py__.coverage" NAME="pytest (api_test.py 内) 覆盖结果" MODIFIED="1746697440749" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/性能优化/ECG-Analysis/apps/api/tests" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$headat.coverage" NAME="hea和dat文件处理 覆盖结果" MODIFIED="1736747209835" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$11_18_Resnet.coverage" NAME="11.18 Resnet模型构建 覆盖结果" MODIFIED="1732272534345" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/pythonProject" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$db_operations.coverage" NAME="db_operations 覆盖结果" MODIFIED="1737531020590" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data_frame" />
    <SUITE FILE_PATH="coverage/00001_hr_hea$api_test.coverage" NAME="api_test 覆盖结果" MODIFIED="1755167954297" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/噪音优化/ECG-Analysis/apps/api/tests" />
  </component>
</project>
import os
import random
import traceback
from collections import namedtuple
from typing import List, Tuple

import joblib
import numpy as np
from scipy import interpolate
from scipy import signal

import threading

from apps.utils.logger_helper import Logger

VlfBand = namedtuple("Vlf_band", ["low", "high"])
LfBand = namedtuple("Lf_band", ["low", "high"])
HfBand = namedtuple("Hf_band", ["low", "high"])
WELCH_METHOD = "welch"
LOMB_METHOD = "lomb"
# 初始化一次
directory = os.path.dirname(__file__)
model_path = os.path.join(directory, 'model/best_multioutput_model.pkl')
scaler_path = os.path.join(directory, 'model/health_metrics_scaler.pkl')
stress_pipeline_path = os.path.join(directory, 'model/stress_pipeline.pkl')
emotion_pipeline_path = os.path.join(directory, 'model/emotion_pipeline.pkl')
_best_model_lock = threading.Lock()

def _get_best_model():
    """惰性加载 best_model；优先尝试 mmap_mode='r' 以降低内存拷贝，失败则回退常规加载。"""
    global best_model
    if best_model is None:
        with _best_model_lock:
            if best_model is None:
                try:
                    try:
                        best_model = joblib.load(model_path, mmap_mode='r')
                        Logger().info("best_model 使用 mmap_mode='r' 加载")
                    except TypeError:
                        # 旧版本joblib或模型不支持mmap参数
                        best_model = joblib.load(model_path)
                        Logger().info("best_model 不支持mmap_mode，已采用常规加载")
                except Exception as e:
                    Logger().error(f"加载 best_model 失败: {str(e)}")
                    raise
    return best_model


best_model = None
try:
    trained_scaler = joblib.load(scaler_path)
except Exception:
    trained_scaler = None
    Logger().warning("未找到训练期scaler，暂用无缩放直推理（建议提供并使用 health_metrics_scaler.pkl）")

# 环境开关：*_PIPELINE_ENABLED 取值 '1/true/on' 启用，其余禁用（默认启用）
_enabled_env = os.getenv('STRESS_PIPELINE_ENABLED', '1').lower()
stress_pipeline_enabled = _enabled_env in ('1', 'true', 'yes', 'on')
_enabled_env_emotion = os.getenv('EMOTION_PIPELINE_ENABLED', '1').lower()
emotion_pipeline_enabled = _enabled_env_emotion in ('1', 'true', 'yes', 'on')
_enabled_env_vitality = os.getenv('VITALITY_RULE_ENABLED', '1').lower()
vitality_rule_enabled = _enabled_env_vitality in ('1', 'true', 'yes', 'on')
_enabled_env_fatigue = os.getenv('FATIGUE_RULE_ENABLED', '1').lower()
fatigue_rule_enabled = _enabled_env_fatigue in ('1', 'true', 'yes', 'on')

# 决策阈值开关（仅供业务方取用）：默认0.5，可通过 STRESS_DECISION_THRESHOLD 调整
try:
    _th = float(os.getenv('STRESS_DECISION_THRESHOLD', '0.5'))
    if not (0.0 < _th < 1.0):
        raise ValueError
    stress_decision_threshold = _th
except Exception:
    stress_decision_threshold = 0.5

if stress_pipeline_enabled:
    try:
        stress_pipeline = joblib.load(stress_pipeline_path)
        Logger().info("已加载 stress_pipeline.pkl，用于压力指数优先预测")
    except Exception:
        stress_pipeline = None
        Logger().info("未找到 stress_pipeline.pkl，将使用原有压力估计逻辑")
else:
    stress_pipeline = None
    Logger().info("已禁用 stress_pipeline（由环境变量 STRESS_PIPELINE_ENABLED 控制）")

if emotion_pipeline_enabled:
    try:
        emotion_pipeline = joblib.load(emotion_pipeline_path)
        Logger().info("已加载 emotion_pipeline.pkl，用于情绪指数优先预测")
    except Exception:
        emotion_pipeline = None
        Logger().info("未找到 emotion_pipeline.pkl，将使用原有情绪估计逻辑")
else:
    emotion_pipeline = None
    Logger().info("已禁用 emotion_pipeline（由环境变量 EMOTION_PIPELINE_ENABLED 控制）")


def get_time_domain_features(nn_intervals: List[float], pnni_as_percent: bool = True) -> dict:
    """

    Parameters
    ----------
    nn_intervals : list
        list of Normal to Normal Interval
    pnni_as_percent: bool
        whether to remove bias or not to compute pnni features.

    Returns
    -------
    time_domain_features : dict
        dictionary containing time domain features for HRV analyses. There are details
        about each features below.

    """

    diff_nni = np.diff(nn_intervals)
    length_int = len(nn_intervals) - 1 if pnni_as_percent else len(nn_intervals)

    # Basic statistics
    mean_nni = np.mean(nn_intervals)
    median_nni = np.median(nn_intervals)
    range_nni = max(nn_intervals) - min(nn_intervals)

    sdsd = np.std(diff_nni)
    rmssd = np.sqrt(np.mean(diff_nni ** 2))

    nni_50 = sum(np.abs(diff_nni) > 50)
    pnni_50 = 100 * nni_50 / length_int
    nni_20 = sum(np.abs(diff_nni) > 20)
    pnni_20 = 100 * nni_20 / length_int

    # Feature found on github and not in documentation
    cvsd = rmssd / mean_nni

    # Features only for long term recordings
    sdnn = np.std(nn_intervals, ddof=1)  # ddof = 1 : unbiased estimator => divide std by n-1
    cvnni = sdnn / mean_nni

    # Heart Rate equivalent features
    heart_rate_list = np.divide(60, nn_intervals)
    mean_hr = np.mean(heart_rate_list)
    min_hr = min(heart_rate_list)
    max_hr = max(heart_rate_list)
    std_hr = np.std(heart_rate_list)

    time_domain_features = {
        'mean_nni': mean_nni,
        'sdnn': sdnn,
        'sdsd': sdsd,
        'nni_50': nni_50,
        'pnni_50': pnni_50,
        'nni_20': nni_20,
        'pnni_20': pnni_20,
        'rmssd': rmssd,
        'median_nni': median_nni,
        'range_nni': range_nni,
        'cvsd': cvsd,
        'cvnni': cvnni,
        'mean_hr': mean_hr,
        "max_hr": max_hr,
        "min_hr": min_hr,
        "std_hr": std_hr,
    }

    return time_domain_features


def get_frequency_domain_features(nn_intervals: List[float], method: str = WELCH_METHOD,
                                  sampling_frequency: int = 4, interpolation_method: str = "linear",
                                  vlf_band: namedtuple = VlfBand(0.003, 0.04),
                                  lf_band: namedtuple = LfBand(0.04, 0.15),
                                  hf_band: namedtuple = HfBand(0.15, 0.40)) -> dict:
    """
    数据质量门槛：当 NN 数<30 或总时长<30s 时，仅返回零化的频域特征，避免伪高。
    """

    nn_intervals = list(nn_intervals)

    # 数据质量检查
    try:
        total_beats = len(nn_intervals)
        total_duration_sec = float(np.sum(nn_intervals)) / 1000.0 if total_beats > 0 else 0.0
    except Exception:
        total_beats = 0
        total_duration_sec = 0.0

    if total_beats < 30 or total_duration_sec < 30:
        return {
            'lf': 0.0,
            'hf': 0.0,
            'lf_hf_ratio': 0.0,
            'lfnu': 0.0,
            'hfnu': 0.0,
            'total_power': 0.0,
            'vlf': 0.0,
        }

    # 计算频谱
    freq, psd = _get_freq_psd_from_nn_intervals(nn_intervals=nn_intervals, method=method,
                                                sampling_frequency=sampling_frequency,
                                                interpolation_method=interpolation_method,
                                                vlf_band=vlf_band, hf_band=hf_band)

    frequency_domain_features = _get_features_from_psd(freq=freq, psd=psd,
                                                       vlf_band=vlf_band,
                                                       lf_band=lf_band,
                                                       hf_band=hf_band)

    return frequency_domain_features


def _get_freq_psd_from_nn_intervals(nn_intervals: List[float], method: str = WELCH_METHOD,
                                    sampling_frequency: int = 4,
                                    interpolation_method: str = "linear",
                                    vlf_band: namedtuple = VlfBand(0.003, 0.04),
                                    hf_band: namedtuple = HfBand(0.15, 0.40)) -> Tuple:
    """
    Returns the frequency and power of the signal.

    Parameters
    ---------
    nn_intervals : list
        list of Normal to Normal Interval
    method : str
        Method used to calculate the psd. Choice are Welch's FFT or Lomb method.
    sampling_frequency : int
        Frequency at which the signal is sampled. Common value range from 1 Hz to 10 Hz,
        by default set to 7 Hz. No need to specify if Lomb method is used.
    interpolation_method : str
        Kind of interpolation as a string, by default "linear". No need to specify if Lomb
        method is used.
    vlf_band : tuple
        Very low frequency bands for features extraction from power spectral density.
    hf_band : tuple
        High frequency bands for features extraction from power spectral density.

    Returns
    ---------
    freq : list
        Frequency of the corresponding psd points.
    psd : list
        Power Spectral Density of the signal.
    """

    timestamp_list = _create_timestamp_list(nn_intervals)

    if method == WELCH_METHOD:
        # ---------- Interpolation of signal ---------- #
        funct = interpolate.interp1d(x=timestamp_list, y=nn_intervals, kind=interpolation_method)

        timestamps_interpolation = _create_interpolated_timestamp_list(nn_intervals, sampling_frequency)
        nni_interpolation = funct(timestamps_interpolation)

        # ---------- Remove DC Component ---------- #
        nni_normalized = nni_interpolation - np.mean(nni_interpolation)

        #  --------- Compute Power Spectral Density  --------- #
        freq, psd = signal.welch(x=nni_normalized, fs=sampling_frequency, window='hann',
                                 nfft=4096)

    # elif method == LOMB_METHOD:
    #     freq, psd = LombScargle(timestamp_list, nn_intervals,
    #                             normalization='psd').autopower(minimum_frequency=vlf_band[0],
    #                                                            maximum_frequency=hf_band[1])
    else:
        raise ValueError("Not a valid method. Choose between 'lomb' and 'welch'")

    return freq, psd


def _create_timestamp_list(nn_intervals: List[float]) -> List[float]:
    """

    Parameters
    ---------
    nn_intervals : list
        List of Normal to Normal Interval.

    Returns
    ---------
    nni_tmstp : list
        list of time intervals between first NN-interval and final NN-interval.
    """
    # Convert in seconds
    nni_tmstp = np.cumsum(nn_intervals) / 1000

    # Force to start at 0
    return nni_tmstp - nni_tmstp[0]


def _create_interpolated_timestamp_list(nn_intervals: List[float], sampling_frequency: int = 7) -> List[float]:
    """

    Parameters
    ---------
    nn_intervals : list
        List of Normal to Normal Interval.
    sampling_frequency : int
        Frequency at which the signal is sampled.

    Returns
    ---------
    nni_interpolation_tmstp : list
        Timestamp for interpolation.
    """
    time_nni = _create_timestamp_list(nn_intervals)
    # Create timestamp for interpolation
    nni_interpolation_tmstp = np.arange(0, time_nni[-1], 1 / float(sampling_frequency))
    return nni_interpolation_tmstp


def _get_features_from_psd(freq: List[float], psd: List[float], vlf_band: namedtuple = VlfBand(0.003, 0.04),
                           lf_band: namedtuple = LfBand(0.04, 0.15),
                           hf_band: namedtuple = HfBand(0.15, 0.40)) -> dict:
    """

    Parameters
    ---------
    freq : array
        Array of sample frequencies.
    psd : list
        Power spectral density or power spectrum.
    vlf_band : tuple
        Very low frequency bands for features extraction from power spectral density.
    lf_band : tuple
        Low frequency bands for features extraction from power spectral density.
    hf_band : tuple
        High frequency bands for features extraction from power spectral density.

    Returns

    """

    # Calcul of indices between desired frequency bands
    vlf_indexes = np.logical_and(freq >= vlf_band[0], freq < vlf_band[1])
    lf_indexes = np.logical_and(freq >= lf_band[0], freq < lf_band[1])
    hf_indexes = np.logical_and(freq >= hf_band[0], freq < hf_band[1])

    # Integrate using the composite trapezoidal rule
    lf = np.trapz(y=psd[lf_indexes], x=freq[lf_indexes])
    hf = np.trapz(y=psd[hf_indexes], x=freq[hf_indexes])

    # total power & vlf : Feature often used for  "long term recordings" analysis
    vlf = np.trapz(y=psd[vlf_indexes], x=freq[vlf_indexes])
    total_power = vlf + lf + hf

    lf_hf_ratio = lf / hf
    lfnu = (lf / (lf + hf)) * 100
    hfnu = (hf / (lf + hf)) * 100

    freqency_domain_features = {
        'lf': lf,
        'hf': hf,
        'lf_hf_ratio': lf_hf_ratio,
        'lfnu': lfnu,
        'hfnu': hfnu,
        'total_power': total_power,
        'vlf': vlf
    }

    return freqency_domain_features


def get_model_feature(rpeaks_hamilton):
    time_domain_features = get_time_domain_features(rpeaks_hamilton)
    frequency_domain_features = get_frequency_domain_features(rpeaks_hamilton)
    fre_list = [round(f, 3) for f in list(time_domain_features.values()) + list(frequency_domain_features.values())]
    return fre_list


def _compute_vitality_rule(rr_intervals):
    """活力规则评分：在无监督前提下的可解释打分（0-100）。"""
    try:
        t = get_time_domain_features(rr_intervals)
        f = get_frequency_domain_features(rr_intervals)
        # z-like简化：用经验中心与尺度粗略标准化，避免极端值
        sdnn = float(t.get('sdnn', 0.0))
        rmssd = float(t.get('rmssd', 0.0))
        tp = float(f.get('total_power', 0.0))
        lf_hf = float(f.get('lf_hf_ratio', 0.0))
        mean_hr = float(t.get('mean_hr', 0.0))
        # 经验中心与尺度（可后续根据数据再调）
        def z(x, mu, sigma):
            if sigma <= 1e-6:
                return 0.0
            return max(-3.0, min(3.0, (x - mu) / sigma))
        sdnn_z = z(sdnn, 40.0, 20.0)
        rmssd_z = z(rmssd, 25.0, 12.0)
        tp_z = z(tp, 500.0, 400.0)
        lf_hf_z = -z(lf_hf, 1.5, 1.0)  # 低更好
        hr_z = -z(mean_hr, 75.0, 15.0) # 适中更好
        # 加权求和
        score_z = 0.3*sdnn_z + 0.3*rmssd_z + 0.2*tp_z + 0.15*lf_hf_z + 0.05*hr_z
        # 映射到0-100
        vitality = (score_z + 3.0) / 6.0 * 100.0
        vitality = float(np.clip(round(vitality, 1), 0.0, 100.0))
        return vitality
    except Exception:
        Logger().warning(f"活力规则评分失败\n{traceback.format_exc()}")
        return None


def sf_model_pred(fre_list):
    """
    fre_list: 用户特征（与训练期一致）
    输出依次是:['压力指数', '情绪指数', '疲劳指数', '活力指数']
    使用训练期 scaler 做 transform，不再单样本 fit。
    """
    X = np.array([fre_list], dtype=float)

    # 处理 NaN 和 inf 值，替换为 0
    X = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)

    if trained_scaler is not None:
        try:
            X_new_scaled = trained_scaler.transform(X)
        except Exception:
            Logger().warning("训练期scaler.transform失败，回退为不缩放的特征")
            X_new_scaled = X
    else:
        X_new_scaled = X

    model = _get_best_model()
    preds = model.predict(X_new_scaled)[0]
    # 合理后处理：clip到[0,100]，保留两位小数
    y_pred = [float(np.clip(round(float(y), 2), 0.0, 100.0)) for y in preds]
    return y_pred


def _predict_stress_with_pipeline(rr_intervals):
    """若有 stress_pipeline 则使用其输出压力指数(0-100)，否则返回 None"""
    if stress_pipeline is None:
        return None
    try:
        fre_list = get_model_feature(rr_intervals)
        X = np.array([fre_list], dtype=float)
        X = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
        prob = float(stress_pipeline.predict_proba(X)[:, 1][0])
        return float(np.clip(round(prob * 100.0, 2), 0.0, 100.0))
    except Exception:
        Logger().warning(f"stress_pipeline 推理失败，回退原逻辑\n{traceback.format_exc()}")
        return None

def _predict_emotion_with_pipeline(rr_intervals):
    """若有 emotion_pipeline 则输出情绪指数(0-100)，否则返回 None"""
    if (not emotion_pipeline_enabled) or (emotion_pipeline is None):
        return None
    try:
        fre_list = get_model_feature(rr_intervals)
        X = np.array([fre_list], dtype=float)
        X = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
        prob = float(emotion_pipeline.predict_proba(X)[:, 1][0])
        return float(np.clip(round(prob * 100.0, 2), 0.0, 100.0))
    except Exception:
        Logger().warning(f"emotion_pipeline 推理失败，回退原逻辑\n{traceback.format_exc()}")
        return None


def _compute_fatigue_rule(rr_intervals):
    """疲劳规则评分：短窗ECG-only初版（0-100）。"""
    try:
        t = get_time_domain_features(rr_intervals)
        f = get_frequency_domain_features(rr_intervals)
        sdnn = float(t.get('sdnn', 0.0))
        rmssd = float(t.get('rmssd', 0.0))
        lf_hf = float(f.get('lf_hf_ratio', 0.0))
        mean_hr = float(t.get('mean_hr', 0.0))
        # 基本直觉：疲劳↑ ≈ 副交感↓(rmssd低)、交感相对↑(lf/hf高)、总体变异性低(sdnn低)、心率偏离适中
        # 经验中心与尺度
        def z(x, mu, sigma):
            if sigma <= 1e-6:
                return 0.0
            return max(-3.0, min(3.0, (x - mu) / sigma))
        sdnn_z = -z(sdnn, 40.0, 20.0)
        rmssd_z = -z(rmssd, 25.0, 12.0)
        lf_hf_z = z(lf_hf, 1.8, 1.0)
        hr_z = z(mean_hr, 85.0, 15.0)  # 偏高心率增加疲劳评分
        score_z = 0.3*sdnn_z + 0.35*rmssd_z + 0.25*lf_hf_z + 0.10*hr_z
        fatigue = (score_z + 3.0) / 6.0 * 100.0
        fatigue = float(np.clip(round(fatigue, 1), 0.0, 100.0))
        return fatigue
    except Exception:
        Logger().warning(f"疲劳规则评分失败\n{traceback.format_exc()}")
        return None


def run_index_main(rr_intervals):
    try:
        # 优先用 stress_pipeline 预测压力
        stress_idx = _predict_stress_with_pipeline(rr_intervals)
        # 优先用 emotion_pipeline 预测情绪
        emotion_idx = _predict_emotion_with_pipeline(rr_intervals)
        # 可选：活力/疲劳规则评分
        vitality_rule = _compute_vitality_rule(rr_intervals) if vitality_rule_enabled else None
        fatigue_rule = _compute_fatigue_rule(rr_intervals) if fatigue_rule_enabled else None

        fre_list = get_model_feature(rr_intervals)
        y_pred = sf_model_pred(fre_list)
        if stress_idx is not None:
            y_pred[0] = stress_idx
        if emotion_idx is not None:
            y_pred[1] = emotion_idx
        if fatigue_rule is not None:
            y_pred[2] = fatigue_rule
        if vitality_rule is not None:
            y_pred[3] = vitality_rule
    except Exception:
        Logger().error(f"模型计算失败，使用保守默认：\n{traceback.format_exc()}")
        y_pred = [60.0, 60.0, 60.0, 60.0]

    return y_pred

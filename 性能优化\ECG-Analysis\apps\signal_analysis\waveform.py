import math
import traceback
import numpy as np
from biosppy.signals import ecg
from pyhrv import time_domain, nonlinear
from scipy.stats import iqr

from apps.utils.logger_helper import Logger


def get_waveform(ecg_signal, sampling_rate):
    """
    处理心电图（ECG）信号，提取波形信息、心率相关数据以及心率变异性（HRV）指标。
    :param ecg_signal:心电图信号数据
    :param sampling_rate:采样率，单位为 Hz。
    :return: 包含波形信息、心率相关数据和 HRV 指标的字典。
            - 'waveform': 波形信息，包括 R 峰、P 峰、Q 峰、S 峰和 T 峰的位置以及 RR间期、均值、标准差、变异系数。
            - 'heart_rate': 心率相关数据，包括心率和基于 RR 间期计算的心率。
            - 'hrv': HRV 相关指标，包括时间域指标和非线性分析指标。
              - 'linear': 时间域指标，包括 、NN20、PNN20、NN50、PNN50、RMSSD、SDNN 和 SDSD。
              - 'nonlinear': 非线性分析指标，包括 SD1、SD2 和 SD 比率。
    :raises: 如果处理过程中发生异常，将捕获并打印异常信息，返回 None。
    """
    try:
        waveform_info = ecg.ecg(signal=ecg_signal, sampling_rate=sampling_rate, show=False)

        waveform_indicators = get_waveform_info(waveform_info, sampling_rate)

        nn_intervals = waveform_indicators['nn_intervals']

        heart_rate = waveform_info['heart_rate']

        return {
            # 波形信息
            'waveform': waveform_indicators,
            # 心率相关数据
            'heart_rate': {
                'max_hr': int(np.max(heart_rate)),
                'min_hr': int(np.min(heart_rate)),
                'hr':  int(np.mean(heart_rate))
            },
            # HRV 相关指标
            'hrv': {
                # 时间域指标
                'linear': get_hrv_linear(nn_intervals),
                # 非线性分析指标
                'nonlinear': get_hrv_nonlinear(nn_intervals)
            }
        }
    except Exception:
        Logger().error(f'获取ECG信息异常：{traceback.format_exc()}')
        return None


def get_waveform_info(waveform_info, sampling_rate):
    """
    获取波形信息
    :param waveform_info: 波形特征
    :param sampling_rate: 采样率
    :return: 波形信息
    """
    r_peaks = waveform_info['rpeaks']
    rr_intervals = np.diff(r_peaks) / sampling_rate
    rr_mean = np.mean(rr_intervals)
    rr_std = np.std(rr_intervals)
    rr_cv = float(rr_std / rr_mean) if rr_mean > 0 else 0.0
    rr_iqr = iqr(rr_intervals)
    rr_diff = np.diff(rr_intervals)

    p_positions = ecg.getPPositions(waveform_info)
    p_peaks = p_positions['P_positions']
    p_start_positions = p_positions['P_start_positions']
    p_end_positions = p_positions['P_end_positions']

    q_positions = ecg.getQPositions(waveform_info)
    q_peaks = q_positions['Q_positions']

    s_positions = ecg.getSPositions(waveform_info)
    s_peaks = s_positions["S_positions"]

    t_positions = ecg.getTPositions(waveform_info)
    t_peaks = t_positions['T_positions']

    rr_diffs = []

    for i in range(len(rr_intervals) - 1):
        rr_diff = rr_intervals[i + 1] - rr_intervals[i]
        rr_diffs.append(rr_diff)

    rr_average = diff_list(r_peaks, [], sampling_rate)
    qrs_average = diff_list(q_peaks, s_peaks, sampling_rate, type='qrs')
    qt_average = diff_list(q_peaks, t_peaks, sampling_rate, type='qt')
    st_average = diff_list(s_peaks, t_peaks, sampling_rate, type='qt')
    pr_average = diff_list(p_peaks, r_peaks, sampling_rate, type='qt')
    pp_average = diff_list(p_start_positions, p_end_positions, sampling_rate, type='pp')
    qtc_average = qt_average / math.sqrt(rr_average)

    # 计算 NN 间期
    nn_intervals = []
    for rr in rr_intervals:
        # 计算对应的心率
        heart_rate = 60.0 / rr if rr != 0 else float('inf')

        # 滤掉不在正常心率范围内的间期
        if 40 <= heart_rate <= 100:
            nn_intervals.append(rr)

    return {
        'r_peaks': r_peaks,
        'p_peaks': p_peaks,
        'p_start_positions': p_start_positions,
        'p_end_positions': p_end_positions,
        'q_peaks': q_peaks,
        's_peaks': s_peaks,
        't_peaks': t_peaks,
        'rr_intervals': rr_intervals,
        'rr_mean': rr_mean,
        'rr_std': rr_std,
        'rr_cv': rr_cv,
        'rr_iqr': rr_iqr,
        'rr_diff': rr_diff,
        'rr_average': rr_average,
        'rr_max': np.max(rr_intervals),
        'rr_min': np.min(rr_intervals),
        'qrs_average': qrs_average,
        'qt_average': qt_average,
        'st_average': st_average,
        'pr_average': pr_average,
        'pp_average': pp_average,
        'qtc_average': qtc_average,
        'nn_intervals': nn_intervals,
        'nn_max': 0 if len(nn_intervals) == 0 else np.max(nn_intervals),
        'nn_min': 0 if len(nn_intervals) == 0 else np.min(nn_intervals)
    }


def get_hrv_linear(nn_intervals):
    """
    获取时间域指标
    :param nn_intervals: RR间期
    :return: 时间域指标
    """
    nn20 = 0
    pnn20 = 0
    nn50 = 0
    pnn50 = 0
    rmssd = 0
    sdnn = 0
    sdsd = 0

    try:
        if len(nn_intervals) > 0:
            result_nn20 = time_domain.nn20(nn_intervals)
            nn20 = result_nn20['nn20']
            pnn20 = result_nn20['pnn20']
            result_nn50 = time_domain.nn50(nn_intervals)
            nn50 = result_nn50['nn50']
            pnn50 = result_nn50['pnn50']
            rmssd = time_domain.rmssd(nn_intervals)['rmssd']
            sdnn = time_domain.sdnn(nn_intervals)['sdnn']

            if len(nn_intervals) > 2:
                sdsd = time_domain.sdsd(nn_intervals)['sdsd']
    except Exception:
        Logger().error(f'获取HRV信息异常：{traceback.format_exc()}')
    finally:
        return {
            'nn20': round(nn20, 1),
            'pnn20': round(pnn20, 1),
            'nn50': round(nn50, 1),
            'pnn50': round(pnn50, 1),
            'rmssd': round(rmssd, 1),
            'sdnn': round(sdnn, 1),
            'sdsd': round(sdsd, 1)
        }


def get_hrv_nonlinear(rr_intervals):
    """
    获取非线性分析指标
    :param rr_intervals: RR间期
    :return: 非线性分析指标
    """
    sd1 = 0
    sd2 = 0
    sd_ratio = 0

    try:
        if len(rr_intervals) > 0:
            result_sd = nonlinear.poincare(rr_intervals, show=False)
            sd1 = result_sd['sd1']
            sd2 = result_sd['sd2']
            sd_ratio = result_sd['sd_ratio']
    except Exception:
        Logger().error(f'获取HRV信息异常：{traceback.format_exc()}')
    finally:
        return {
            'sd1': sd1,
            'sd2': sd2,
            'sd_ratio': sd_ratio
        }


def diff_list(numbers, numbers_end, sampling_rate, type='rr'):
    # 假设我们有一个列表
    # numbers = [1, 4, 7, 10, 13]
    # 初始化一个空列表来存储差值
    differences = []
    if type == 'rr':
        # 遍历列表中的元素，除了最后一个元素（因为我们没有“后一个数”来计算差值）
        for i in range(len(numbers) - 1):
            # 计算当前元素与下一个元素的差值，并添加到differences列表中
            difference = numbers[i + 1] - numbers[i]
            differences.append(difference)
    elif type == 'pp':
        if len(numbers) == len(numbers_end):
            differences = [numbers_end[i] - numbers[i] for i in range(len(numbers))]
        else:
            numbers = [842, 1018, 1507, 2150, 2857, 3586, 3925, 4276]
            numbers_end = [859, 1032, 1540, 2209, 2912, 3629, 3970, 4319]
            differences = [numbers_end[i] - numbers[i] for i in range(len(numbers))]
    else:
        differences = [numbers_end[i] - numbers[i] for i in range(len(numbers))]
    average_num = (sum(differences) / len(differences)) / sampling_rate
    return average_num

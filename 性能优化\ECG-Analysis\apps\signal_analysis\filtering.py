import numpy as np
import pywt
from scipy.signal import butter, filtfilt, medfilt

def wavelet_filter(ecg_signal, sampling_rate, wavelet='sym5',
                   lowcut=0.05, highcut=100.0,
                   drift_window=2.0, threshold_scale=0.5):
    """
    改进型心电滤波：优化低频保留和基线处理
    """
    n = len(ecg_signal)

    # === 第一步：动态计算分解层数 ===
    max_level = pywt.dwt_max_level(n, pywt.Wavelet(wavelet).dec_len)
    target_level = int(np.log2(sampling_rate / 0.5))
    level = min(max_level, max(5, target_level))

    # === 自适应调整滤波参数 ===
    if sampling_rate <= 100:
        median_win_factor = 100
        lowcut = max(lowcut, 0.05)  # 提高低频下限
        highcut = min(highcut, sampling_rate / 4)
    elif sampling_rate <= 200:
        median_win_factor = 200
        lowcut = max(lowcut, 0.05)
        highcut = min(highcut, sampling_rate / 3)
    else:
        median_win_factor = 300
        lowcut = max(lowcut, 0.05)
        highcut = min(highcut, 100)

    # === 第二步：优化高通滤波器 ===
    nyq = 0.5 * sampling_rate
    b, a = butter(2, lowcut / nyq, btype='highpass')  # 降低滤波器阶数
    ecg_pre = filtfilt(b, a, ecg_signal)

    # === 第三步：中值滤波 ===
    median_win = max(3, int(sampling_rate / median_win_factor))
    if median_win % 2 == 0: median_win += 1
    ecg_med = medfilt(ecg_pre, kernel_size=median_win)

    # === 第四步：改进的小波去噪 ===
    coeffs = pywt.wavedec(ecg_med, wavelet, level=level)

    sigma = np.median(np.abs(coeffs[-1])) / 0.6745
    universal_thresh = sigma * np.sqrt(2 * np.log(n))

    for i in range(len(coeffs)):
        coeff_thresh = universal_thresh * threshold_scale
        if i == 0:  # 近似系数（最低频成分）
            coeff_thresh *= 0.1  # 更宽松的阈值
        coeffs[i] = pywt.threshold(coeffs[i], coeff_thresh, mode='soft')

    ecg_wavelet = pywt.waverec(coeffs, wavelet)

    # === 第五步：优化基线漂移处理 ===
    win_size = int(drift_window * sampling_rate)
    if win_size % 2 == 0: win_size += 1

    half_win = win_size // 2
    baseline = np.zeros(n)
    for i in range(n):
        left = max(0, i - half_win)
        right = min(n, i + half_win + 1)
        baseline[i] = np.mean(ecg_wavelet[left:right])

    ecg_final = ecg_wavelet[:n] - baseline

    # === 可选：高频噪声抑制 ===
    if highcut < nyq:
        b, a = butter(3, highcut / nyq, btype='lowpass')  # 降低低通滤波器阶数
        ecg_final = filtfilt(b, a, ecg_final)

    # === 新增：心电特征保护机制 ===
    window_size = 50
    mean_vals = np.convolve(ecg_final, np.ones(window_size) / window_size, mode='same')
    std_vals = np.array([np.std(ecg_final[max(0, i - window_size // 2):i + window_size // 2 + 1]) for i in range(n)])

    dynamic_threshold = mean_vals + 3 * std_vals

    for i in range(n):
        if abs(ecg_final[i]) > dynamic_threshold[i]:
            if 0 < i < n - 1:
                ecg_final[i] = (ecg_final[i - 1] + ecg_final[i + 1]) / 2

    return ecg_final
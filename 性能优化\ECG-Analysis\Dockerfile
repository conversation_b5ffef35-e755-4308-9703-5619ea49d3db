# 建立 python 环境
FROM python:3.9

# 镜像作者
MAINTAINER zhouj

# 设置 python 环境变量
ENV PYTHONUNBUFFERED 1

# 设置容器内工作目录
WORKDIR /app

# 将当前目录文件加入到容器工作目录中
ADD . .

# 将本地时区文件复制到容器中
RUN cp ./Shanghai /etc/localtime

# 设置 PyPI 镜像源
RUN python3 -m pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
RUN python3 -m pip config set install.trusted-host mirrors.aliyun.com

# 安装依赖
RUN python3 -m pip install -r requirements.txt

# 设置 manage.py 文件可执行权限
RUN chmod +x ./manage.py


         3024040 function calls (3022582 primitive calls) in 329.821 seconds

   Ordered by: internal time

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
       46  291.935    6.346  291.935    6.346 {method 'recv_into' of '_socket.socket' objects}
        3   23.717    7.906   23.717    7.906 {built-in method builtins.input}
        6    8.603    1.434    8.603    1.434 {method 'connect' of '_socket.socket' objects}
       16    4.750    0.297    4.750    0.297 {built-in method time.sleep}
    13/11    0.172    0.013  305.510   27.774 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:639(ecg_analysis)
   390096    0.086    0.000    0.157    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:150(_isna)
   390096    0.061    0.000    0.218    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:67(isna)
       11    0.055    0.005    0.056    0.005 {method 'read' of '_io.TextIOWrapper' objects}
       13    0.051    0.004    0.052    0.004 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\encoder.py:306(iterencode)
   390047    0.050    0.000    0.050    0.000 {built-in method pandas._libs.missing.checknull}
   195000    0.049    0.000    0.168    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:288(notna)
       11    0.045    0.004  305.686   27.790 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:967(process_single_file)
   585222    0.033    0.000    0.033    0.000 {pandas._libs.lib.is_scalar}
      325    0.023    0.000    0.023    0.000 {method 'write' of '_io.TextIOWrapper' objects}
   361804    0.020    0.000    0.020    0.000 {method 'append' of 'list' objects}
216472/216440    0.015    0.000    0.017    0.000 {built-in method builtins.isinstance}
   195000    0.011    0.000    0.011    0.000 {built-in method builtins.abs}
   165167    0.009    0.000    0.009    0.000 {method 'strip' of 'str' objects}
       11    0.007    0.001    0.007    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\decoder.py:343(raw_decode)
       21    0.006    0.000    0.006    0.000 {built-in method io.open}
       75    0.005    0.000    0.005    0.000 {built-in method nt.stat}
      327    0.004    0.000    0.004    0.000 {method 'split' of 'str' objects}
      325    0.003    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:288(__init__)
      419    0.002    0.000    0.002    0.000 {built-in method builtins.max}
       26    0.002    0.000    0.002    0.000 {method 'sendall' of '_socket.socket' objects}
       68    0.002    0.000    0.002    0.000 {built-in method builtins.min}
     4830    0.002    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:670(__getitem__)
      190    0.002    0.000    0.003    0.000 {pandas._libs.lib.maybe_convert_objects}
     5103    0.002    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:742(__iter__)
       10    0.002    0.000    0.002    0.000 {method 'close' of '_io.TextIOWrapper' objects}
      325    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1514(findCaller)
     4830    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:740(encodekey)
      337    0.001    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:180(split)
       28    0.001    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2494(getproxies_environment)
       42    0.001    0.000    0.001    0.000 {built-in method winreg.QueryValueEx}
     6133    0.001    0.000    0.001    0.000 {method 'lower' of 'str' objects}
1153/1093    0.001    0.000    0.001    0.000 {built-in method numpy.array}
6744/5749    0.001    0.000    0.001    0.000 {built-in method builtins.len}
       10    0.001    0.000    0.001    0.000 {pandas._libs.writers.write_csv_rows}
      429    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:124(splitdrive)
       11    0.001    0.000    0.001    0.000 {built-in method _codecs.utf_8_decode}
       23    0.001    0.000    0.001    0.000 {built-in method builtins.sum}
       48    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:227(_encode_invalid_chars)
      325    0.001    0.000    0.027    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1073(emit)
     4704    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:693(__iter__)
      647    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:44(normcase)
     4830    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:734(check_str)
      210    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:470(sanitize_array)
       28    0.001    0.000    0.001    0.000 {built-in method winreg.OpenKey}
      325    0.001    0.000    0.041    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1565(_log)
      325    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1062(flush)
        6    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:219(__init__)
       13    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:301(makefile)
      161    0.001    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:120(_take_nd_ndarray)
      347    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:121(_splitext)
     2374    0.001    0.000    0.001    0.000 {built-in method builtins.hasattr}
      303    0.001    0.000    0.038    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1436(info)
      201    0.001    0.000    0.001    0.000 {method 'reduce' of 'numpy.ufunc' objects}
      325    0.001    0.000    0.029    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1645(callHandlers)
     2123    0.001    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\generic.py:43(_check)
      303    0.001    0.000    0.039    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2074(info)
      130    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5517(__finalize__)
       11    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:392(raw_decode)
     4982    0.000    0.000    0.000    0.000 {method 'upper' of 'str' objects}
      317    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:121(put)
    40/20    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:397(__new__)
      325    0.000    0.000    0.028    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:941(handle)
      441    0.000    0.000    0.000    0.000 {method 'match' of 're.Pattern' objects}
      325    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:655(format)
       80    0.000    0.000    0.011    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3463(__getitem__)
    17/14    0.000    0.000  300.553   21.468 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:594(urlopen)
      150    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1579(maybe_cast_to_datetime)
      141    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:366(urlparse)
       60    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:323(__init__)
      464    0.000    0.000    0.000    0.000 {built-in method numpy.empty}
       40    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexers\utils.py:249(maybe_convert_indices)
      347    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:203(splitext)
      325    0.000    0.000    0.008    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1550(makeRecord)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2022(_form_blocks)
       11    0.000    0.000  305.510   27.774 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:620(profile_api_call)
     2791    0.000    0.000    0.000    0.000 {built-in method builtins.getattr}
     1107    0.000    0.000    0.000    0.000 {method 'replace' of 'str' objects}
      130    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:239(__init__)
      135    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:351(putheader)
      650    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:898(acquire)
       22    0.000    0.000    0.000    0.000 {pandas._libs.ops.scalar_compare}
      472    0.000    0.000    0.001    0.000 {built-in method _abc._abc_instancecheck}
   156/46    0.000    0.000    0.000    0.000 {built-in method _abc._abc_subclasscheck}
        9    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:710(_slice_take_blocks_ax0)
      155    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:417(urlsplit)
       31    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:369(parse_url)
       40    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:5613(_cmp_method)
      325    0.000    0.000    0.029    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1591(handle)
  344/290    0.000    0.000    0.001    0.000 {built-in method numpy.core._multiarray_umath.implement_array_function}
      208    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1466(maybe_infer_to_datetimelike)
      325    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:435(_format)
      135    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1208(putheader)
       13    0.000    0.000  291.940   22.457 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:481(getresponse)
     1053    0.000    0.000    0.000    0.000 {method 'rfind' of 'str' objects}
      161    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:554(_take_preprocess_indexer_and_fill_value)
      476    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1689(isEnabledFor)
       17    0.000    0.000    8.609    0.506 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:365(request)
      606    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1587(_is_dtype_type)
       70    0.000    0.000    0.012    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:587(__init__)
     1380    0.000    0.000    0.000    0.000 {method 'get' of 'dict' objects}
       69    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5192(equals)
       29    0.000    0.000    0.000    0.000 {method '_rebuild_blknos_and_blklocs' of 'pandas._libs.internals.BlockManager' objects}
      143    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1116(take_nd)
      161    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:57(take_nd)
        8    0.000    0.000    0.000    0.000 {built-in method nt.get_terminal_size}
       14    0.000    0.000  300.558   21.468 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:613(send)
       10    0.000    0.000    0.019    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3376(to_csv)
        6    0.000    0.000    0.000    0.000 {function socket.close at 0x0000024E03ADB550}
      100    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:824(update)
       14    0.000    0.000  300.560   21.469 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:673(send)
       75    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:91(_path_join)
      708    0.000    0.000    0.000    0.000 {method 'acquire' of '_thread.RLock' objects}
       14    0.000    0.000  300.632   21.474 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:500(request)
        3    0.000    0.000    0.000    0.000 {built-in method io.open_code}
      325    0.000    0.000    0.000    0.000 {method 'flush' of '_io.TextIOWrapper' objects}
  589/583    0.000    0.000    0.000    0.000 {method 'encode' of 'str' objects}
      129    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2084(debug)
      210    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:698(_try_cast)
      202    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5577(__setattr__)
       14    0.000    0.000    0.061    0.004 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:457(prepare_request)
       17    0.000    0.000  300.549   17.679 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:379(_make_request)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:33(select_wait_for_socket)
       40    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3609(take)
      334    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:341(notify)
       27    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2105(_merge_blocks)
      324    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:110(_coerce_args)
       40    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\array_ops.py:227(comparison_op)
      337    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:214(basename)
       17    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1052(putrequest)
      325    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:918(format)
       47    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:81(RLock)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:95(_default_key_normalizer)
       58    0.000    0.000    0.000    0.000 {method 'clear' of 'dict' objects}
      190    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numeric.py:268(full)
        6    0.000    0.000    0.000    0.000 {built-in method builtins.print}
      325    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:160(<lambda>)
      394    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:46(__setitem__)
       40    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1106(take)
      650    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:905(release)
      179    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1991(get_block_type)
      325    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:633(usesTime)
       40    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexers\utils.py:457(check_array_indexer)
      620    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_list_like}
       98    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:61(merge_setting)
       10    0.000    0.000    0.009    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:601(get_handle)
        2    0.000    0.000    0.000    0.000 {built-in method nt.listdir}
       50    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:634(reindex_indexer)
      325    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1306(current_thread)
      325    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:364(getMessage)
      325    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:119(getLevelName)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:204(iterencode)
       20    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:672(_with_infer)
      325    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:427(usesTime)
      112    0.000    0.000  291.935    2.607 {method 'readline' of '_io.BufferedReader' objects}
       14    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:204(get_netrc_auth)
       40    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:872(take)
     1871    0.000    0.000    0.000    0.000 {built-in method nt.fspath}
        3    0.000    0.000    0.000    0.000 {built-in method marshal.loads}
       40    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis0_int64_int64}
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:32(seterr)
       83    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2074(_stack_arrays)
       90    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:109(_get_single_key)
       41    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1258(__init__)
       59    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:391(array_equivalent)
     1441    0.000    0.000    0.000    0.000 {built-in method builtins.issubclass}
       11    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:204(parse_headers)
      151    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:657(get)
       14    0.000    0.000    0.052    0.004 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:494(prepare_body)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2240(is_unique)
      650    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:796(filter)
       69    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2043(new_block)
       22    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:218(_parsegen)
       10    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:596(_homogenize)
      208    0.000    0.000    0.000    0.000 {pandas._libs.lib.infer_datetimelike_array}
       99    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:345(to_key_val_list)
       83    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:221(require)
       13    0.000    0.000  291.937   22.457 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:300(begin)
      325    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:438(format)
      390    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:51(__getitem__)
       40    0.000    0.000    0.009    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3530(_getitem_bool_array)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:471(_parse_headers)
      223    0.000    0.000    0.000    0.000 {method 'search' of 're.Pattern' objects}
       13    0.000    0.000  291.935   22.457 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:267(_read_status)
       10    0.000    0.000    0.000    0.000 {method 'get_slice' of 'pandas._libs.internals.BlockManager' objects}
      207    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:161(is_object_dtype)
       10    0.000    0.000    0.000    0.000 {built-in method pandas._libs.missing.isnaobj}
      681    0.000    0.000    0.000    0.000 {method 'rstrip' of 'str' objects}
       46    0.000    0.000  291.935    6.346 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:655(readinto)
      377    0.000    0.000    0.000    0.000 {built-in method __new__ of type object at 0x00007FFC17CEB810}
      410    0.000    0.000    0.000    0.000 {built-in method time.time}
      472    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\abc.py:96(__instancecheck__)
      477    0.000    0.000    0.000    0.000 {method 'find' of 'str' objects}
       69    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:40(__init__)
       10    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:306(_save_chunk)
       14    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:409(prepare_url)
      658    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:909(__len__)
      553    0.000    0.000    0.000    0.000 {method 'startswith' of 'str' objects}
      114    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:321(_name_get)
        3    0.000    0.000    0.000    0.000 {method 'read' of '_io.BufferedReader' objects}
       29    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2153(to_native_types)
       40    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:2988(_construct_result)
       39    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:227(_isna_array)
      516    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:52(bounded_int)
      161    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:581(is_dtype_equal)
       58    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:289(expanduser)
      120    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:640(name)
      325    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:1031(name)
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:132(geterr)
       40    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:689(<listcomp>)
      280    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1851(dtype)
      143    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:267(make_block_same_class)
       27    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:521(cookiejar_from_dict)
      150    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1067(convert)
       19    0.000    0.000    0.000    0.000 {pandas._libs.lib.infer_dtype}
  512/462    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:14(asarray)
      325    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:639(formatMessage)
      365    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:34(_get_bothseps)
      475    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1552(get_dtype)
      150    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1038(iget_values)
       81    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:166(_consolidate_key)
      245    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:438(ensure_wrapped_if_datetimelike)
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:53(shape)
       90    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:127(_get_option)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:337(extend)
     1204    0.000    0.000    0.000    0.000 {method 'decode' of 'bytes' objects}
       30    0.000    0.000    0.000    0.000 {method 'settimeout' of '_socket.socket' objects}
       14    0.000    0.000    0.011    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:750(merge_environment_settings)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:229(asarray_tuplesafe)
      135    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1240(is_float_dtype)
      117    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1274(is_bool_dtype)
      109    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7004(ensure_index)
      190    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(copyto)
      126    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:155(hostname)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:560(__init__)
      210    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:630(_sanitize_ndim)
       15    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap_external>:1498(find_spec)
       14    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:114(proxy_bypass)
       90    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:589(_get_root)
      134    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:468(maybe_promote)
       59    0.000    0.000    0.000    0.000 {method 'astype' of 'numpy.ndarray' objects}
       10    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:934(_list_of_dict_to_arrays)
      907    0.000    0.000    0.000    0.000 {method 'endswith' of 'str' objects}
       10    0.000    0.000    0.004    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:296(_save_body)
       14    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:765(should_bypass_proxies)
       14    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:483(prepare_headers)
       39    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:289(_compile)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:476(readinto)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:658(__init__)
      140    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:195(_hostinfo)
      250    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:379(extract_array)
      150    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:389(is_timedelta64_dtype)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1962(construct_1d_object_array_from_listlike)
       61    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:654(_simple_new)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:251(_get_filepath_or_buffer)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1026(iget)
      151    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1424(debug)
      325    0.000    0.000    0.000    0.000 {built-in method sys._getframe}
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:177(__init__)
       10    0.000    0.000    0.000    0.000 {pandas._libs.missing.isnaobj2d}
      130    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:47(__init__)
      180    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:603(_get_deprecated_option)
       22    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:858(_raw_read)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\ntpath.py:77(join)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1689(<listcomp>)
      325    0.000    0.000    0.000    0.000 {built-in method nt.getpid}
       88    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:462(get)
       36    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis0_bool_bool}
      280    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:321(is_hashable)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:258(__init__)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:868(__new__)
       11    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:359(build_response)
       96    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:1962(maybe_coerce_values)
      534    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:58(<genexpr>)
       18    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_1d_int64_int64}
      334    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:246(__enter__)
      161    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:326(_get_take_nd_function)
        9    0.000    0.000    0.000    0.000 {method 'get_indexer' of 'pandas._libs.index.IndexEngine' objects}
       40    0.000    0.000    0.007    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3708(_take_with_is_copy)
       14    0.000    0.000    0.056    0.004 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:351(prepare)
      510    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:147(<lambda>)
       10    0.000    0.000    0.018    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:1131(to_csv)
        6    0.000    0.000    0.000    0.000 {built-in method _socket.getaddrinfo}
       88    0.000    0.000    0.001    0.000 {built-in method builtins.sorted}
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:153(get)
       13    0.000    0.000  291.938   22.457 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1300(getresponse)
       10    0.000    0.000    0.005    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4719(reindex)
       10    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:971(_finalize_columns_and_data)
      708    0.000    0.000    0.000    0.000 {method 'release' of '_thread.RLock' objects}
      120    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5561(__getattr__)
      658    0.000    0.000    0.000    0.000 {built-in method _thread.get_ident}
       41    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:877(__init__)
       11    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:253(apply)
        6    0.000    0.000    8.604    1.434 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:27(create_connection)
      153    0.000    0.000    0.000    0.000 {built-in method builtins.all}
       10    0.000    0.000    0.016    0.002 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:236(save)
       13    0.000    0.000    0.052    0.004 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\encoder.py:277(encode)
       31    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:263(_remove_path_dot_segments)
       20    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7123(_maybe_cast_data_without_dtype)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1353(add_cookie_header)
      280    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:575(dtype)
      148    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1148(needs_i8_conversion)
       69    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1219(vals_sorted_by_key)
      510    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:145(classes)
       40    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis0_object_object}
       60    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:970(from_blocks)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:130(filterwarnings)
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:82(__init__)
       40    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\common.py:55(new_method)
        7    0.000    0.000    0.000    0.000 {built-in method builtins.__build_class__}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1034(get_data)
      334    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:261(_is_owned)
       40    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2357(check_bool_indexer)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:643(__init__)
       57    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:572(na_value_for_dtype)
       11    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:59(parsestr)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1596(make_cookies)
        6    0.000    0.000    8.604    1.434 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:193(_new_conn)
       88    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:293(header_source_parse)
       31    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:100(__new__)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:183(dumps)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:139(__init__)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:573(__init__)
       90    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2486(check_deprecated_indexers)
       51    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:109(__init__)
      160    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:590(name)
      156    0.000    0.000    0.000    0.000 {method 'any' of 'numpy.ndarray' objects}
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:315(__init__)
      132    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:259(__getitem__)
      130    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:83(allows_duplicate_labels)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:845(_engine)
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:35(__init__)
       20    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1451(_format_native_types)
      100    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:560(_get_axis)
       10    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5009(_reindex_with_indexers)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:105(is_bool_indexer)
       13    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:237(__init__)
      334    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:249(__exit__)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\array_ops.py:58(comp_method_OBJECT_ARRAY)
       14    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:76(proxy_bypass_registry)
     1193    0.000    0.000    0.000    0.000 {built-in method builtins.ord}
      115    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:676(items)
       14    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:446(get_connection_with_tls_context)
       28    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:838(select_proxy)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4987(__contains__)
       11    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:910(read)
      150    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2006(_grouping_func)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:444(read)
       27    0.000    0.000    0.000    0.000 {method 'argsort' of 'numpy.ndarray' objects}
      169    0.000    0.000    0.000    0.000 {built-in method builtins.any}
       44    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:564(get_content_type)
        9    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:878(get_disease_name)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1018(get_auth_from_url)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1745(from_array)
      373    0.000    0.000    0.000    0.000 {built-in method builtins.hash}
       95    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:664(__contains__)
       10    0.000    0.000    0.000    0.000 {method 'writerow' of '_csv.writer' objects}
       76    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:57(__iter__)
      135    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:400(<genexpr>)
       20    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1109(_is_binary_mode)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:338(_format_native_types)
       29    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:631(to_native_types)
      166    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1045(_validate_header_part)
       50    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5632(_protect_consolidate)
       44    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:497(get_all)
       69    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2057(check_ndim)
       14    0.000    0.000  300.632   21.474 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:626(post)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1677(extract_cookies)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\array_ops.py:134(_na_arithmetic_op)
      135    0.000    0.000    0.000    0.000 {method 'fullmatch' of 're.Pattern' objects}
      210    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:687(_maybe_repeat)
       32    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:255(inner)
      153    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:130(_validate_timeout)
      334    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:245(_qsize)
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7099(maybe_extract_name)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\algorithms.py:1356(take)
      418    0.000    0.000    0.000    0.000 {method 'join' of 'str' objects}
       40    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3923(_get_item_cache)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:428(increment)
      121    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:78(readline)
      120    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1721(validate_all_hashable)
       90    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:255(__call__)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:50(__init__)
      317    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:248(_put)
       22    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:124(extract_cookies_to_jar)
       10    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1951(create_block_manager_from_column_arrays)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:223(encoded_labels)
        9    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:3714(get_indexer)
        9    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2088(_consolidate)
      143    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:281(_sanitize_header)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:64(parse_parts)
       42    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\genericpath.py:16(exists)
        4    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:2051(<listcomp>)
       11    0.000    0.000    0.063    0.006 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:274(load)
      391    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4834(_values)
       88    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:302(add)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:493(_parse)
       10    0.000    0.000    0.005    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:102(arrays_to_mgr)
       90    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:571(_select_options)
       80    0.000    0.000    0.000    0.000 {built-in method numpy.seterrobj}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:390(is_dataclass)
      464    0.000    0.000    0.000    0.000 {method 'partition' of 'str' objects}
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\request.py:189(body_to_chunks)
      190    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:552(require_length_match)
      170    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:358(iget)
      210    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:667(_sanitize_str_dtypes)
       10    0.000    0.000    0.006    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:505(nested_data_to_arrays)
       40    0.000    0.000    0.000    0.000 {method 'take' of 'numpy.ndarray' objects}
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:90(_urllib3_request_context)
      174    0.000    0.000    0.001    0.000 {method 'join' of 'bytes' objects}
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:255(get)
       20    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3411(_ixs)
       69    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1223(deepvalues)
       90    0.000    0.000    0.000    0.000 {method 'view' of 'numpy.ndarray' objects}
      270    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:353(<genexpr>)
       83    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:1034(check_header_validity)
       82    0.000    0.000    0.000    0.000 {method 'copy' of 'numpy.ndarray' objects}
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:572(prepare_content_length)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3908(_slice)
       10    0.000    0.000    0.005    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:4788(reindex)
      240    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1740(<genexpr>)
       22    0.000    0.000    0.000    0.000 {method 'readlines' of '_io._IOBase' objects}
       60    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1714(__init__)
      156    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_methods.py:53(_any)
       20    0.000    0.000    0.000    0.000 {built-in method numpy.arange}
      170    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:546(_get_axis_number)
       50    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5650(f)
       14    0.000    0.000    0.000    0.000 {method 'Close' of 'PyHKEY' objects}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:3906(_box_col_values)
       11    0.000    0.000    0.007    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\decoder.py:332(decode)
       30    0.000    0.000    8.606    0.287 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:939(send)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:168(_number_format)
       99    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1429(is_extension_array_dtype)
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1862(internal_values)
      177    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:884(__len__)
       14    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:305(connection_from_context)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:101(push)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1042(is_numeric_v_string_like)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:182(encode)
       11    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:41(parse)
       10    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:556(check_parent_directory)
      334    0.000    0.000    0.000    0.000 {method 'acquire' of '_thread.lock' objects}
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:347(dtype)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:433(__enter__)
       14    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:330(connection_from_pool_key)
       11    0.000    0.000    0.007    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\__init__.py:299(loads)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:53(__init__)
       10    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4123(reindex)
       31    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:167(_simple_new)
       90    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:642(_warn_if_deprecated)
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:106(_encode_params)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:189(_data)
      211    0.000    0.000    0.000    0.000 {method 'rpartition' of 'str' objects}
       17    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:258(_get_conn)
       92    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:834(_reset_identity)
       40    0.000    0.000    0.006    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\arraylike.py:38(__eq__)
      120    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:184(is_array_like)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1683(_consolidate_check)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numeric.py:2317(array_equal)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:732(close)
       17    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:331(putrequest)
     12/4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:71(_compile)
       69    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1750(__iter__)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:495(_array_equivalent_object)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:458(__enter__)
      571    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:1149(cast)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:202(__init__)
      162    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2093(<lambda>)
      219    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:156(blknos)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5037(__getitem__)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:82(atleast_2d)
      234    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:55(<genexpr>)
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:238(helper)
       11    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:242(__init__)
        6    0.000    0.000    0.000    0.000 {method 'setsockopt' of '_socket.socket' objects}
       10    0.000    0.000    0.387    0.039 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:525(get_token)
       53    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:720(__hash__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:497(infer_compression)
      121    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:128(__next__)
       10    0.000    0.000    0.006    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:798(to_arrays)
       10    0.000    0.000    0.000    0.000 {pandas._libs.lib.fast_unique_multiple_list_gen}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1372(_clear_cache)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:238(_new_conn)
       61    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1675(is_consolidated)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:40(assert_header_parsing)
       17    0.000    0.000    8.606    0.506 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:998(_send_output)
       45    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:350(wrapper)
       14    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2662(getproxies_registry)
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:117(__exit__)
       50    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:5646(_consolidate_inplace)
       60    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:566(_get_block_manager_axis)
       58    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2121(extend_blocks)
      219    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1416(is_1d_only_ea_dtype)
      183    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\util.py:19(to_str)
       96    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1747(pandas_dtype)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:1527(_get_slice_axis)
       15    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2041(error)
       28    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:542(merge_cookies)
        4    0.000    0.000    0.002    0.001 <frozen importlib._bootstrap>:890(_find_spec)
       48    0.000    0.000    0.000    0.000 {method 'subn' of 're.Pattern' objects}
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:140(get_cookie_header)
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:687(_values)
       32    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:768(__instancecheck__)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:223(vstack)
       83    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:298(<setcomp>)
        8    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1337(add_argument)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:144(_initialize_columns)
       40    0.000    0.000    0.000    0.000 {method 'nonzero' of 'numpy.ndarray' objects}
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\common.py:75(get_op_result_name)
      160    0.000    0.000    0.000    0.000 {built-in method numpy.geterrobj}
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:954(__getitem__)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:554(_dtype_to_subclass)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:679(_init_length)
      334    0.000    0.000    0.000    0.000 {method '__enter__' of '_thread.lock' objects}
       56    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:238(fill_value)
       15    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1465(error)
       60    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:1413(__len__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:27(__init__)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:95(__getitem__)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:348(_get_timeout)
       98    0.000    0.000    0.000    0.000 {method 'ravel' of 'numpy.ndarray' objects}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:181(_add_filter)
      114    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:307(_name_includes_bit_suffix)
      152    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:977(_output)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:268(_isna_string_dtype)
       13    0.000    0.000    0.052    0.004 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\__init__.py:276(dumps)
       10    0.000    0.000    0.000    0.000 {built-in method _csv.writer}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\exceptions.py:142(__init__)
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1483(is_ea_or_datetimelike_dtype)
       10    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:4615(_reindex_axes)
      278    0.000    0.000    0.000    0.000 {method 'items' of 'dict' objects}
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:522(equals)
      154    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\utils.py:51(_has_surrogates)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:207(register_hook)
       32    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:771(__subclasscheck__)
       33    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:890(content)
      130    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:328(attrs)
        9    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_bool_bool}
        8    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:161(__init__)
       69    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:803(is_)
       10    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1073(<listcomp>)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:546(request_url)
   156/46    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\abc.py:100(__subclasscheck__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:127(is_url)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:870(quote_from_bytes)
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:914(get_code)
       44    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:29(_splitparam)
      143    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\_policybase.py:311(header_fetch_parse)
       20    0.000    0.000    0.000    0.000 {built-in method _codecs.utf_8_encode}
       11    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:947(json)
       14    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:276(connection_from_host)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:316(close)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\base.py:744(__iter__)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:542(_set_axis)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:120(__init__)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:296(_put_conn)
       34    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:188(clone)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:15(default_hooks)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:438(__exit__)
       11    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:319(decode)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:140(has_mi_columns)
      115    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:698(__init__)
      228    0.000    0.000    0.000    0.000 {pandas._libs.algos.ensure_platform_int}
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:781(get_adapter)
       18    0.000    0.000    0.000    0.000 {built-in method _operator.eq}
      302    0.000    0.000    0.000    0.000 {method 'pop' of 'dict' objects}
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:816(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:247(new)
       90    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:346(apply_if_callable)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:334(__init__)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\missing.py:138(dispatch_fill_zeros)
       13    0.000    0.000    0.000    0.000 {method 'close' of '_io.BufferedReader' objects}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:658(_parse_args)
      179    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:354(dtype)
       19    0.000    0.000    0.000    0.000 {pandas._libs.lib.array_equivalent_object}
       56    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:680(is_integer_dtype)
       37    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:303(_normalize_host)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:624(unquote)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:825(__array__)
      121    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_asarray.py:86(asanyarray)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:76(copy)
      246    0.000    0.000    0.000    0.000 {pandas._libs.algos.ensure_object}
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\shutil.py:1312(get_terminal_size)
       17    0.000    0.000    8.606    0.506 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1236(endheaders)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:135(super_len)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:909(text)
      324    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:99(_noop)
        9    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_int64_int64}
       19    0.000    0.000    0.000    0.000 {method 'pop' of 'collections.OrderedDict' objects}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1039(__new__)
       10    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:4652(_reindex_columns)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:411(close)
       14    0.000    0.000    0.011    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:826(get_environ_proxies)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:312(<listcomp>)
       11    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1693(_consolidate_inplace)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:390(__init__)
       59    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:694(readable)
       22    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:1038(stream)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:134(__init__)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:304(cert_verify)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:539(get_encoding_from_headers)
      260    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:349(flags)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:678(_from_parts)
      219    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:172(blklocs)
       54    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(concatenate)
       37    0.000    0.000    0.000    0.000 {method 'extend' of 'list' objects}
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:485(urlunsplit)
       10    0.000    0.000    0.000    0.000 {pandas._libs.lib.dicts_to_array}
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:774(get_proxy)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:514(_parse_content_type_header)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:208(write_cols)
       14    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\request.py:2707(getproxies)
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\contextlib.py:108(__enter__)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2323(convert_to_index_sliceable)
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:538(find)
       75    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:114(<listcomp>)
       44    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:735(_error_catcher)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:31(encode)
        9    0.000    0.000    0.000    0.000 {pandas._libs.algos.take_2d_axis1_object_object}
       41    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:1017(_handle_fromlist)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:534(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:1(<module>)
       20    0.000    0.000    0.000    0.000 {method 'get_loc' of 'pandas._libs.index.IndexEngine' objects}
       20    0.000    0.000    0.000    0.000 {method 'remove' of 'list' objects}
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:474(urlunparse)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_validators.py:258(validate_axis_style_args)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:3585(get_loc)
       15    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:892(urlencode)
       22    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:801(_fp_read)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\construction.py:805(is_empty_data)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:799(quote)
        4    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap>:986(_find_and_load)
       14    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:610(prepare_cookies)
       75    0.000    0.000    0.000    0.000 {built-in method builtins.next}
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numerictypes.py:360(issubdtype)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:202(__init__)
      173    0.000    0.000    0.000    0.000 {method 'setdefault' of 'dict' objects}
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2116(<listcomp>)
       28    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\types.py:171(__get__)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:199(__init__)
       32    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:3925(_set_is_copy)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\base.py:45(__len__)
       11    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\__init__.py:459(loads)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:195(stringify_path)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:588(prepare_auth)
      120    0.000    0.000    0.000    0.000 {method 'lstrip' of 'str' objects}
       86    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:1711(sanitize_to_nanoseconds)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\computation\expressions.py:223(evaluate)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:2032(new_block_2d)
       10    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:103(close)
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numerictypes.py:286(issubclass_)
       88    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:479(set_raw)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:944(_getitem_slice)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:630(prepare_hooks)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:459(<listcomp>)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:468(to_native_types)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1417(is_dir)
       29    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:252(make_block)
       11    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\simplejson\decoder.py:379(decode)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:85(path_url)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:890(__array__)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:437(__init__)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:183(_userinfo)
       18    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(array_equal)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:374(_merge_pool_kwargs)
       20    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_all_arraylike}
        4    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap_external>:1367(_get_spec)
       22    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:816(generate)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:514(_construct_axes_from_arguments)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\dtypes.py:1206(is_dtype)
       50    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:618(consolidate)
       29    0.000    0.000    0.000    0.000 {method 'update' of 'dict' objects}
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1731(clear_expired_cookies)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:799(mount)
       96    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:155(<lambda>)
      160    0.000    0.000    0.000    0.000 {pandas._libs.lib.item_from_zerodim}
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:1079(closed)
       97    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\_internal_utils.py:25(to_native_string)
       48    0.000    0.000    0.000    0.000 {method 'count' of 'bytes' objects}
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\threading.py:222(__init__)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:213(get_payload)
       90    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\_config\config.py:630(_translate_key)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:104(__init__)
       28    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:358(update)
       39    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:533(is_string_or_object_np_dtype)
       99    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:291(__iter__)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\idna.py:147(encode)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1091(is_datetimelike_v_numeric)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:309(__init__)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:84(<listcomp>)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:155(_expand_user)
       11    0.000    0.000    0.000    0.000 {built-in method _thread.allocate_lock}
       46    0.000    0.000    0.000    0.000 {method '_checkReadable' of '_io._IOBase' objects}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:146(splitroot)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:200(_has_aliases)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:3794(_get_indexer)
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:497(is_categorical_dtype)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\api.py:331(default_index)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6298(_maybe_cast_indexer)
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:354(cache_from_source)
       60    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:160(cast_scalar_indexer)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4952(<genexpr>)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:268(_save_header)
       22    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:178(_call_parse)
      334    0.000    0.000    0.000    0.000 {method '__exit__' of '_thread.lock' objects}
      114    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_dtype.py:24(_kind_name)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:166(port)
       39    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\re.py:250(compile)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1255(__init__)
       10    0.000    0.000    0.005    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:263(_save)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6018(_should_compare)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:534(treat_as_nested)
        1    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:2046(<listcomp>)
       11    0.000    0.000    0.000    0.000 {method 'readinto' of '_io.BufferedReader' objects}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\warnings.py:477(__exit__)
      190    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:1043(copyto)
       27    0.000    0.000    0.000    0.000 {method 'max' of 'numpy.ndarray' objects}
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\url.py:351(_encode_target)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:660(requote_uri)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:159(resolve_redirects)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:4997(_needs_reindex_multi)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:718(__str__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1478(_get_optional_kwargs)
       45    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\base.py:286(is_dtype)
      240    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\base.py:326(ndim)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\fromnumeric.py:999(argsort)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:393(prepare_method)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:313(__call__)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:244(__init__)
       48    0.000    0.000    0.000    0.000 {method 'decode' of 'bytearray' objects}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\weakref.py:517(__init__)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:202(start_connect)
       47    0.000    0.000    0.000    0.000 {method 'copy' of 'dict' objects}
       25    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:164(host)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:475(get_adjustment)
       26    0.000    0.000    0.000    0.000 {function SocketIO.close at 0x0000024E03ADBF70}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2440(is_object)
     12/4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:174(getwidth)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:446(_init_decoder)
       62    0.000    0.000    0.000    0.000 {method 'groups' of 're.Match' objects}
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:799(iter_content)
       16    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:389(parent)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:251(put)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:636(unquote_unreserved)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\typing.py:802(__getitem__)
       92    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:164(__getitem__)
       40    0.000    0.000    0.000    0.000 {built-in method sys.intern}
       11    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:173(feed)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:212(_expand_lang)
       28    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4860(_get_engine_target)
      130    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\flags.py:51(allows_duplicate_labels)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:635(release_conn)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4013(_validate_positional_slice)
       60    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\frame.py:578(_constructor)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1294(_cookie_attrs)
       44    0.000    0.000    0.000    0.000 {method 'format' of 'str' objects}
      138    0.000    0.000    0.000    0.000 {built-in method builtins.callable}
       66    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:434(isclosed)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:245(read_timeout)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:211(<genexpr>)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:130(iloc)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:786(is_unsigned_integer_dtype)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:184(close)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:1238(_set_as_cached)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:379(_is_method_retryable)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:289(get_backoff_time)
       27    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(argsort)
       33    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:588(get_content_maintype)
       27    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(vstack)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\ops\dispatch.py:11(should_extension_dispatch)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_ufunc_config.py:429(__init__)
        7    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1448(warning)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:901(getaddrinfo)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4244(_maybe_preserve_names)
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\poolmanager.py:229(_new_pool)
       22    0.000    0.000    0.000    0.000 {method 'reshape' of 'numpy.ndarray' objects}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:945(parent)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:496(close)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:396(build_connection_pool_key_attributes)
       10    0.000    0.000    0.005    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\util\_decorators.py:322(wrapper)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6313(_validate_indexer)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:15(is_connection_dropped)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:299(is_connected)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\array_algos\take.py:288(_get_take_nd_function_cached)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:113(wait_for_read)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:486(_decref_socketios)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:1496(is_complex_dtype)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2280(is_boolean)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:122(pushlines)
      117    0.000    0.000    0.000    0.000 {method 'keys' of 'dict' objects}
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:3837(_check_indexing_method)
       70    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1658(name)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2660(_isnan)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:679(_initialize_justify)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:958(<genexpr>)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:197(_new_message)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:16(<dictcomp>)
       78    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:222(_verbose_message)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:97(_intenum_converter)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:70(close)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:157(_get_module_lock)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\fromnumeric.py:52(_wrapfunc)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:423(flush)
       80    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:916(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:759(compile)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\missing.py:911(clean_reindex_fill_method)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\computation\expressions.py:63(_evaluate_standard)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:536(_compile_info)
       27    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(atleast_2d)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\compat\numpy\function.py:49(__call__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:919(__getitem__)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:459(is_interval_dtype)
        5    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\__init__.py:43(normalize_encoding)
        9    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:924(get_multi_label_disease_name)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:240(init_poolmanager)
       60    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:945(dtype)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:33(__init__)
       47    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:233(__next)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1193(stat)
       60    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:1806(_block)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:121(_get_index_label_from_obj)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1183(_validate_method)
       93    0.000    0.000    0.000    0.000 {method 'values' of 'collections.OrderedDict' objects}
        4    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap>:956(_find_and_load_unlocked)
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:126(resolve_default_timeout)
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\structures.py:60(__len__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:785(truncate)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:743(__len__)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:9(is_fp_closed)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:900(default_headers)
       11    0.000    0.000    0.000    0.000 {method 'tobytes' of 'memoryview' objects}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:435(_parse_sub)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:147(username)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5919(_index_as_unique)
       46    0.000    0.000    0.000    0.000 {method '_checkClosed' of '_io._IOBase' objects}
       96    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:150(classes_and_not_datetimelike)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5931(_maybe_promote)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:239(is_fsspec_url)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:204(_need_to_save_header)
       10    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:1490(_getitem_axis)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:477(_init_module_attrs)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:403(retries)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1398(_add_action)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:937(parse)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:631(__new__)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_methods.py:37(_amax)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:925(close)
       10    0.000    0.000    0.000    0.000 <string>:2(__init__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:586(_format_args)
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap>:650(_load_unlocked)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\common.py:732(is_signed_integer_dtype)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2604(inferred_type)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:685(_initialize_columns)
       37    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1308(register)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1549(_fill_cache)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2151(_preprocess_slice_or_indexer)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\request.py:134(set_file_position)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:761(_is_in_terminal)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\_internal_utils.py:38(unicode_is_ascii)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:107(get_redirect_target)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:130(_get_index_label_flat)
       61    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_integer}
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:251(_get)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:769(is_redirect)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2685(isna)
        2    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\__init__.py:70(search_function)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2650(_na_value)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:554(_get_axis_name)
       50    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:222(items)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:423(__init__)
       17    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1330(_path_importer_cache)
        9    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:799(<listcomp>)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\socket.py:492(_real_close)
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:406(_close_conn)
       36    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:44(_debug)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:47(CSVFormatter)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:387(is_retry)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\dataclasses.py:1045(is_dataclass)
       10    0.000    0.000    0.000    0.000 {method 'fill' of 'numpy.ndarray' objects}
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:451(items)
       10    0.000    0.000    0.001    0.000 {built-in method _codecs.lookup}
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\parser.py:17(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:515(_maybe_promote)
       81    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:244(mgr_locs)
       44    0.000    0.000    0.000    0.000 {method 'count' of 'str' objects}
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:218(_acquireLock)
       14    0.000    0.000    0.000    0.000 <string>:1(__new__)
        1    0.000    0.000    0.002    0.002 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:508(parse_arguments)
        9    0.000    0.000    0.000    0.000 {pandas._libs.internals.get_blkno_placements}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:701(_format_parsed_parts)
       14    0.000    0.000    0.000    0.000 {method 'sort' of 'list' objects}
        9    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:918(<listcomp>)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:333(_iterencode_dict)
       10    0.000    0.000    0.003    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:1051(_convert_object_array)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1638(__init__)
       23    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:135(_path_stat)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:689(spec_from_file_location)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1132(_maybe_disallow_fill)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:104(header)
       22    0.000    0.000    0.000    0.000 {method 'read' of '_io.StringIO' objects}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:728(__fspath__)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:1049(_init)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:242(<genexpr>)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\exceptions.py:17(__init__)
       38    0.000    0.000    0.000    0.000 {method 'pop' of 'list' objects}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2352(is_floating)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:219(_vhstack_dispatcher)
       22    0.000    0.000    0.000    0.000 {method 'write' of '_io.BytesIO' objects}
       18    0.000    0.000    0.000    0.000 {method 'rstrip' of 'bytes' objects}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:993(_validate_or_indexify_columns)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:186(__init__)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:241(start)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:179(data_index)
       29    0.000    0.000    0.000    0.000 {built-in method pandas._libs.writers.word_len}
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:84(__init__)
        6    0.000    0.000    8.604    1.434 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:278(connect)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:717(_calc_max_cols_fitted)
       60    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_iterator}
       18    0.000    0.000    0.000    0.000 {method 'all' of 'numpy.ndarray' objects}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:112(_initialize_index_label)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:728(_calc_max_rows_fitted)
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:579(translation)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:210(_pop_message)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:525(__repr__)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\cookiejar.py:1287(_cookies_for_request)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2059(warning)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\locale.py:384(normalize)
      121    0.000    0.000    0.000    0.000 {method 'popleft' of 'collections.deque' objects}
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:706(<setcomp>)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:5279(identical)
        3    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:842(exec_module)
       10    0.000    0.000    0.002    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:123(__exit__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1055(_maybe_memory_map)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:78(acquire)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:181(is_multipart)
       60    0.000    0.000    0.000    0.000 {built-in method _warnings._filters_mutated}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:638(_compile_bytecode)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1589(_add_action)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1809(_parse_known_args)
       10    0.000    0.000    0.000    0.000 {built-in method _codecs.lookup_error}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\exceptions.py:96(__init__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\util.py:31(reraise)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\message.py:303(set_payload)
       22    0.000    0.000    0.000    0.000 {method 'extend' of 'collections.deque' objects}
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\_methods.py:56(_all)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:409(is_exhausted)
       28    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:743(set_environ)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\series.py:523(_constructor)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1774(parse_known_args)
       28    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:701(__len__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py:691(_from_parsed_parts)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:227(connect_timeout)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:724(<listcomp>)
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:127(_path_split)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:81(__init__)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:110(__init__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:809(__init__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:163(_initialize_chunksize)
       44    0.000    0.000    0.000    0.000 {method 'end' of 're.Match' objects}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:103(release)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:93(_set_socket_options)
       11    0.000    0.000    0.000    0.000 {method 'write' of '_io.StringIO' objects}
       78    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:392(__subclasshook__)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1192(_validate_path)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:91(merge_hooks)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:222(count_not_none)
       28    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:753(value)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:270(from_int)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:208(_arrays_for_stack_dispatcher)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\locale.py:350(_replace_encoding)
       61    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_float}
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\hooks.py:22(dispatch_hook)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:661(_initialize_sparsify)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1179(_encode_request)
        9    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:79(_unpack_uint32)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:167(_path_isabs)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:276(_optimize_charset)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\enum.py:938(__and__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:233(mgr_to_mgr)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:553(_classify_pyc)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1493(_get_spec)
       39    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:172(append)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\generic.py:543(<dictcomp>)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:2125(<listcomp>)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:462(get_compression_method)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\_distutils_hack\__init__.py:102(find_spec)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:99(_can_hold_na)
       40    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\numeric.py:331(_is_all_dates)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:959(<genexpr>)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:377(_check_close)
       56    0.000    0.000    0.000    0.000 {method 'items' of 'collections.OrderedDict' objects}
        8    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2480(_get_formatter)
       12    0.000    0.000    0.000    0.000 {method 'split' of 'bytes' objects}
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\util.py:7(to_bytes)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:549(module_from_spec)
       54    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:143(concatenate)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_collections.py:102(__setitem__)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:2632(_is_multi)
       31    0.000    0.000    0.000    0.000 {method 'values' of 'dict' objects}
       42    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:254(get)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1567(__init__)
        2    0.000    0.000    0.001    0.000 {built-in method builtins.__import__}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4240(_wrap_reindex_result)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexing.py:2473(need_slice)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:287(step)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:260(__init__)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:92(na_rep)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:58(__init__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:294(is_named_tuple)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:540(_ensure_array)
       20    0.000    0.000    0.000    0.000 {method 'insert' of 'list' objects}
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:343(<genexpr>)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:570(_metavar_formatter)
       21    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:916(<genexpr>)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:456(<genexpr>)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\inference.py:98(is_file_like)
       25    0.000    0.000    0.000    0.000 {built-in method time.perf_counter}
       11    0.000    0.000    0.000    0.000 {method 'getvalue' of '_io.BytesIO' objects}
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:108(index)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:134(_initialize_quotechar)
       15    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:64(_relax_case)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1675(getEffectiveLevel)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:193(nlevels)
       28    0.000    0.000    0.000    0.000 {method 'update' of 'collections.OrderedDict' objects}
       18    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:129(<genexpr>)
        9    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:919(<listcomp>)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\connection.py:103(allowed_gai_family)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:147(__enter__)
        6    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:376(cached)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\construction.py:965(<listcomp>)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7185(unpack_nested_dtype)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:1019(__init__)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:851(__init__)
       44    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:248(__len__)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:413(_iterencode)
       44    0.000    0.000    0.000    0.000 {method 'seek' of '_io.StringIO' objects}
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:1200(_validate_host)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\sessions.py:805(<listcomp>)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:693(_initialize_colspace)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:1(<module>)
        2    0.000    0.000    0.000    0.000 {built-in method _imp.is_builtin}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:484(_get_cached)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:598(_code)
       19    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\missing.py:107(clean_fill_method)
       10    0.000    0.000    0.000    0.000 {built-in method builtins.setattr}
        4    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap_external>:1399(find_spec)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:140(__init__)
        8    0.000    0.000    0.000    0.000 {method 'fileno' of '_io.TextIOWrapper' objects}
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1312(_registry_get)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:160(__len__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\exceptions.py:34(__init__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\timeout.py:172(from_float)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\common.py:226(<genexpr>)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:264(stop)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:365(_is_connection_error)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\response.py:465(_decode)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\compat\_optional.py:87(import_optional_dependency)
       18    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:867(__exit__)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:399(_checknetloc)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\proxy.py:11(connection_requires_http_tunnel)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:100(decimal)
       10    0.000    0.000    0.000    0.000 {built-in method _stat.S_ISDIR}
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\missing.py:626(is_valid_na_for_dtype)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:224(__init__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:7063(ensure_has_len)
       22    0.000    0.000    0.000    0.000 {method 'truncate' of '_io.StringIO' objects}
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1747(_add_action)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1317(_path_hooks)
       44    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:117(info)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:629(is_truncated_horizontally)
       26    0.000    0.000    0.000    0.000 {method 'reverse' of 'list' objects}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:176(cb)
       18    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:863(__enter__)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:217(is_single_block)
       17    0.000    0.000    0.000    0.000 {built-in method time.monotonic}
        1    0.000    0.000    0.000    0.000 <frozen zipimport>:63(__init__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1514(_pop_action_class)
        3    0.000    0.000    0.000    0.000 {built-in method builtins.exec}
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:1149(_normalize_host)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:145(_path_is_mode_type)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1075(path_stats)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:461(_get_literal_prefix)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:355(_escape)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:227(_releaseLock)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:578(copy)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:1(<module>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\codecs.py:94(__new__)
       12    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:111(__init__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:853(quote_plus)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1417(setLevel)
       30    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\csvs.py:96(float_format)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:343(_sleep_backoff)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:4106(_validate_can_reindex)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6307(_maybe_cast_listlike_indexer)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:249(_compile_charset)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\idna.py:300(getregentry)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:183(_constructor)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:35(_new_module)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1459(__init__)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:286(tell)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\urllib\parse.py:391(_splitnetloc)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:891(default_user_agent)
       20    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:229(disallow_kwargs)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:202(__init__)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:579(format)
       18    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\numeric.py:2313(_array_equal_dispatcher)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\fromnumeric.py:995(_argsort_dispatcher)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\_request_methods.py:51(__init__)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:586(_validate_timestamp_pyc)
       17    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:339(_validate_conn)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:216(<genexpr>)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1527(_check_conflict)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:1276(disable)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:342(__init__)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\cookies.py:87(get_new_headers)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1388(add_argument_group)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:516(_check_name_wrapper)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\response.py:79(<listcomp>)
       14    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\adapters.py:578(add_headers)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:666(_initialize_formatters)
        6    0.000    0.000    0.000    0.000 {built-in method sys.exc_info}
        6    0.000    0.000    0.000    0.000 {built-in method _socket.getdefaulttimeout}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:151(__exit__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:72(_check_methods)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:921(fix_flags)
        1    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:1581(<dictcomp>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:360(_raise_timeout)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:982(view)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:154(_path_isfile)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:423(_simple)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\http\client.py:862(_get_hostport)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:349(sleep)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\blocks.py:638(copy)
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:1130(_get_binary_io_classes)
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:660(dgettext)
       28    0.000    0.000    0.000    0.000 {method 'isalnum' of 'str' objects}
        1    0.000    0.000    0.001    0.001 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\importlib\__init__.py:109(import_module)
       27    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\shape_base.py:78(_atleast_2d_dispatcher)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:82(_have_working_poll)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\utils.py:581(iter_slices)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:633(is_truncated_vertically)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:121(getregentry)
       26    0.000    0.000    0.000    0.000 {built-in method _imp.acquire_lock}
       13    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:295(is_closed)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1518(_get_handler)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1954(consume_positionals)
        4    0.000    0.000    0.000    0.000 {built-in method nt._path_splitroot}
       26    0.000    0.000    0.000    0.000 {built-in method _imp.release_lock}
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:595(isstring)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:800(find_spec)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:76(__init__)
       22    0.000    0.000    0.000    0.000 {method 'append' of 'collections.deque' objects}
        2    0.000    0.000    0.000    0.000 {built-in method builtins.locals}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:725(find_spec)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\wait.py:95(wait_for_socket)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connectionpool.py:94(__str__)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:507(_maybe_promote_cached)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:282(<dictcomp>)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\formats\format.py:747(_adjust_max_rows)
        4    0.000    0.000    0.000    0.000 {built-in method _sre.compile}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1590(path_hook_for_FileFinder)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\json\encoder.py:259(_make_iterencode)
       11    0.000    0.000    0.000    0.000 {method 'pop' of 'set' objects}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1004(__init__)
       22    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\email\feedparser.py:125(__iter__)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:596(copy_func)
        4    0.000    0.000    0.000    0.000 {built-in method _imp.is_frozen}
        1    0.000    0.000    0.001    0.001 <frozen importlib._bootstrap>:1002(_gcd_import)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:411(<listcomp>)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\connection.py:183(host)
        9    0.000    0.000    0.000    0.000 d:/Project/噪音优化/ECG-Analysis/apps/api/tests/api_test.py:962(<listcomp>)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:492(_get_charset_prefix)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:297(<lambda>)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:574(<listcomp>)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:211(_call_with_frames_removed)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1578(<setcomp>)
        7    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:453(_get_iscased)
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:1906(nlevels)
        9    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:6036(_is_comparable_dtype)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:249(match)
       13    0.000    0.000    0.000    0.000 {function HTTPResponse.flush at 0x0000024E03BAE820}
       14    0.000    0.000    0.000    0.000 {method 'add' of 'set' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\exceptions.py:46(__init__)
        3    0.000    0.000    0.000    0.000 {built-in method _imp._fix_co_filename}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:143(__init__)
       21    0.000    0.000    0.000    0.000 {built-in method _sre.unicode_iscased}
        2    0.000    0.000    0.000    0.000 {method 'flush' of '_io.BufferedReader' objects}
       10    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\io\common.py:120(__enter__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1031(__init__)
        9    0.000    0.000    0.000    0.000 {built-in method from_bytes}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:881(_find_spec_legacy)
       11    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:302(__subclasshook__)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\queue.py:242(_init)
        8    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:81(groups)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:649(_ensure_dtype_type)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2414(_get_value)
        3    0.000    0.000    0.001    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\gettext.py:735(gettext)
        4    0.000    0.000    0.000    0.000 {method 'find' of 'bytearray' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_compile.py:432(_generate_overlap_table)
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\urllib3\util\retry.py:373(_is_read_error)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\requests\models.py:997(raise_for_status)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\range.py:439(_view)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:2106(_match_arguments_partial)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_collections_abc.py:406(__subclasshook__)
        6    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:168(__setitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:909(__init__)
        3    0.000    0.000    0.000    0.000 {method 'index' of 'str' objects}
        8    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1465(<genexpr>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\os.py:1073(__subclasshook__)
        1    0.000    0.000    0.000    0.000 <__array_function__ internals>:2(can_cast)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:193(_checkLevel)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2018(getLogger)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\dtypes\cast.py:454(ensure_dtype_can_hold_na)
       19    0.000    0.000    0.000    0.000 {built-in method _sre.unicode_tolower}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\indexes\base.py:785(_view)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:886(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1759(_get_positional_actions)
        1    0.000    0.000    0.000    0.000 {method 'disable' of '_lsprof.Profiler' objects}
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:159(_path_isdir)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\pandas\core\internals\managers.py:599(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1767(parse_args)
        2    0.000    0.000    0.000    0.000 {built-in method _json.encode_basestring}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:839(create_module)
        3    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\logging\__init__.py:2157(handle)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\six.py:184(find_module)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:937(_sanity_check)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1029(get_filename)
        4    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\sre_parse.py:162(__delitem__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:10(NetrcParseError)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\netrc.py:22(netrc)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:26(IncrementalEncoder)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:397(has_location)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1760(<listcomp>)
        2    0.000    0.000    0.000    0.000 {method 'isascii' of 'str' objects}
        2    0.000    0.000    0.000    0.000 {built-in method sys.audit}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:604(<listcomp>)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\argparse.py:1240(__init__)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:49(IncrementalDecoder)
        1    0.000    0.000    0.000    0.000 {built-in method atexit.register}
        2    0.000    0.000    0.000    0.000 {method 'with_traceback' of 'BaseException' objects}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\numpy\core\multiarray.py:468(can_cast)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:85(StreamWriter)
        1    0.000    0.000    0.000    0.000 {built-in method builtins.id}
        2    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\_distutils_hack\__init__.py:109(<lambda>)
        1    0.000    0.000    0.000    0.000 {built-in method builtins.iter}
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\importlib_metadata\_compat.py:44(find_spec)
        1    0.000    0.000    0.000    0.000 C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\encodings\utf_8_sig.py:97(StreamReader)
        1    0.000    0.000    0.000    0.000 {pandas._libs.lib.is_bool}



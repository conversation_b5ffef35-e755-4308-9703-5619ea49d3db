20250814 18:42:27 - ERROR -SNA诊断异常：
Traceback (most recent call last):
  File "D:\Project\ECG-Analysis-Minute\apps\analysis\arrhythmia_diagnosis\sna.py", line 52, in process
    freqs, psd = welch(rr_intervals, nperseg=min(len(rr_intervals), 256))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 449, in welch
    freqs, Pxx = csd(x, x, fs=fs, window=window, nperseg=nperseg,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 581, in csd
    freqs, _, Pxy = _spectral_helper(x, y, fs, window, nperseg, noverlap, nfft,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1830, in _spectral_helper
    result = _fft_helper(x, win, detrend_func, nperseg, noverlap, nfft, sides)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1915, in _fft_helper
    result = func(result, n=nfft)
SystemError: <uarray multimethod 'rfft'> returned NULL without setting an error

20250814 18:49:45 - ERROR -SNA诊断异常：
Traceback (most recent call last):
  File "D:\Project\ECG-Analysis-Minute\apps\analysis\arrhythmia_diagnosis\sna.py", line 52, in process
    freqs, psd = welch(rr_intervals, nperseg=min(len(rr_intervals), 256))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 449, in welch
    freqs, Pxx = csd(x, x, fs=fs, window=window, nperseg=nperseg,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 581, in csd
    freqs, _, Pxy = _spectral_helper(x, y, fs, window, nperseg, noverlap, nfft,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1830, in _spectral_helper
    result = _fft_helper(x, win, detrend_func, nperseg, noverlap, nfft, sides)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1915, in _fft_helper
    result = func(result, n=nfft)
SystemError: <uarray multimethod 'rfft'> returned NULL without setting an error

20250814 18:49:45 - ERROR -SNA诊断异常：
Traceback (most recent call last):
  File "D:\Project\ECG-Analysis-Minute\apps\analysis\arrhythmia_diagnosis\sna.py", line 52, in process
    freqs, psd = welch(rr_intervals, nperseg=min(len(rr_intervals), 256))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 449, in welch
    freqs, Pxx = csd(x, x, fs=fs, window=window, nperseg=nperseg,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 581, in csd
    freqs, _, Pxy = _spectral_helper(x, y, fs, window, nperseg, noverlap, nfft,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1830, in _spectral_helper
    result = _fft_helper(x, win, detrend_func, nperseg, noverlap, nfft, sides)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1915, in _fft_helper
    result = func(result, n=nfft)
SystemError: <uarray multimethod 'rfft'> returned NULL without setting an error

20250814 18:49:45 - ERROR -SNA诊断异常：
Traceback (most recent call last):
  File "D:\Project\ECG-Analysis-Minute\apps\analysis\arrhythmia_diagnosis\sna.py", line 52, in process
    freqs, psd = welch(rr_intervals, nperseg=min(len(rr_intervals), 256))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 449, in welch
    freqs, Pxx = csd(x, x, fs=fs, window=window, nperseg=nperseg,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 581, in csd
    freqs, _, Pxy = _spectral_helper(x, y, fs, window, nperseg, noverlap, nfft,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1830, in _spectral_helper
    result = _fft_helper(x, win, detrend_func, nperseg, noverlap, nfft, sides)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1915, in _fft_helper
    result = func(result, n=nfft)
SystemError: <uarray multimethod 'rfft'> returned NULL without setting an error

20250814 18:49:46 - ERROR -SNA诊断异常：
Traceback (most recent call last):
  File "D:\Project\ECG-Analysis-Minute\apps\analysis\arrhythmia_diagnosis\sna.py", line 52, in process
    freqs, psd = welch(rr_intervals, nperseg=min(len(rr_intervals), 256))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 449, in welch
    freqs, Pxx = csd(x, x, fs=fs, window=window, nperseg=nperseg,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 581, in csd
    freqs, _, Pxy = _spectral_helper(x, y, fs, window, nperseg, noverlap, nfft,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1830, in _spectral_helper
    result = _fft_helper(x, win, detrend_func, nperseg, noverlap, nfft, sides)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1915, in _fft_helper
    result = func(result, n=nfft)
SystemError: <uarray multimethod 'rfft'> returned NULL without setting an error

20250814 18:49:46 - ERROR -SNA诊断异常：
Traceback (most recent call last):
  File "D:\Project\ECG-Analysis-Minute\apps\analysis\arrhythmia_diagnosis\sna.py", line 52, in process
    freqs, psd = welch(rr_intervals, nperseg=min(len(rr_intervals), 256))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 449, in welch
    freqs, Pxx = csd(x, x, fs=fs, window=window, nperseg=nperseg,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 581, in csd
    freqs, _, Pxy = _spectral_helper(x, y, fs, window, nperseg, noverlap, nfft,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1830, in _spectral_helper
    result = _fft_helper(x, win, detrend_func, nperseg, noverlap, nfft, sides)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1915, in _fft_helper
    result = func(result, n=nfft)
SystemError: <uarray multimethod 'rfft'> returned NULL without setting an error

20250814 18:49:46 - ERROR -SNA诊断异常：
Traceback (most recent call last):
  File "D:\Project\ECG-Analysis-Minute\apps\analysis\arrhythmia_diagnosis\sna.py", line 52, in process
    freqs, psd = welch(rr_intervals, nperseg=min(len(rr_intervals), 256))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 449, in welch
    freqs, Pxx = csd(x, x, fs=fs, window=window, nperseg=nperseg,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 581, in csd
    freqs, _, Pxy = _spectral_helper(x, y, fs, window, nperseg, noverlap, nfft,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1830, in _spectral_helper
    result = _fft_helper(x, win, detrend_func, nperseg, noverlap, nfft, sides)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1915, in _fft_helper
    result = func(result, n=nfft)
SystemError: <uarray multimethod 'rfft'> returned NULL without setting an error

20250814 18:49:46 - ERROR -SNA诊断异常：
Traceback (most recent call last):
  File "D:\Project\ECG-Analysis-Minute\apps\analysis\arrhythmia_diagnosis\sna.py", line 52, in process
    freqs, psd = welch(rr_intervals, nperseg=min(len(rr_intervals), 256))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 449, in welch
    freqs, Pxx = csd(x, x, fs=fs, window=window, nperseg=nperseg,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 581, in csd
    freqs, _, Pxy = _spectral_helper(x, y, fs, window, nperseg, noverlap, nfft,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1830, in _spectral_helper
    result = _fft_helper(x, win, detrend_func, nperseg, noverlap, nfft, sides)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\scipy\signal\spectral.py", line 1915, in _fft_helper
    result = func(result, n=nfft)
SystemError: <uarray multimethod 'rfft'> returned NULL without setting an error


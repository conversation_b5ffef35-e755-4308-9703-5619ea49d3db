"""
房颤检测主模块
包含房颤检测和分析的相关函数
"""
import traceback
import numpy as np
from apps.utils.logger_helper import Logger
from .af_config import AF_WEIGHTS, THRESHOLDS, LEAD_SPECIFIC, DUAL_SCORE_CONFIG, AUTOCORR_CONFIG
from .af_signal_processing import (
    detect_p_waves_phasor, tq_segment_analysis, detect_p_waves_cwt, 
    detect_f_waves
)
from .af_thresholds import evaluate_signal_quality, get_adaptive_thresholds, get_adaptive_weights
from .af_utils import analyze_rr_intervals, calculate_af_score, combine_p_wave_detections

_internal_computation_cache = {}
LOG_DETAIL_LEVEL = 1

def log_info(message, detail_level=1):
    """优化的日志记录，根据全局详细级别过滤"""
    if detail_level <= LOG_DETAIL_LEVEL:
        Logger().info(message)

def log_error(message, exception=None):
    """优化的错误日志，只在需要时包含完整堆栈跟踪"""
    if exception is not None:
        if LOG_DETAIL_LEVEL >= 2:
            Logger().error(f"{message}: {traceback.format_exc()}")
        else:
            Logger().error(f"{message}: {str(exception)}")
    else:
        Logger().error(message)

def clear_all_caches():
    """清除所有缓存数据，包括内部计算缓存和依赖模块的缓存"""
    global _internal_computation_cache
    _internal_computation_cache.clear()
    log_info("内部计算缓存已清除 (_internal_computation_cache)")
    
    log_info("所有相关缓存已清除或确认移除")

def detect_atrial_fibrillation(waveform_info, lead_type='I', precomputed_signal_features=None):
    """
    房颤诊断算法
    """
    try:
        global _internal_computation_cache
        _internal_computation_cache = {}
        
        if not waveform_info or 'waveform' not in waveform_info:
            return False

        waveform = waveform_info['waveform']
        ecg_signal = waveform_info.get('signal', None)
        sampling_rate = waveform_info.get('sampling_rate', 250)

        rr_intervals = waveform.get('rr_intervals', [])
        r_peaks = waveform.get('r_peaks', [])

        if len(rr_intervals) < 6 or len(r_peaks) < 6:
            return False

        p_end_positions = waveform.get('p_end_positions', [])
        t_peaks = waveform.get('t_peaks', [])
        p_start_positions = waveform.get('p_start_positions', [])

        # 使用 af_utils.analyze_rr_intervals 获取统一的RR特征
        rr_features = analyze_rr_intervals(waveform)
        rr_mean = rr_features['rr_mean']
        rr_std = rr_features['rr_std']
        rr_cv = rr_features['rr_cv']
        rr_iqr = rr_features['rr_iqr']
        rr_autocorr = rr_features['rr_autocorr']  # 从RR特征中获取自相关值

        f_wave_score = waveform.get('f_wave_score', 0)
        f_wave_freq = waveform.get('f_wave_freq', 0)
        f_wave_regularity = waveform.get('f_wave_regularity', 1)

        original_rp_diff = len(r_peaks) - len(p_end_positions)

        p_wave_enhanced = False

        p_wave_enhanced, p_start_positions, p_end_positions, f_wave_features = enhance_p_wave_detection(
            lead_type, ecg_signal, r_peaks, p_start_positions, p_end_positions,
            t_peaks, sampling_rate
        )

        if p_wave_enhanced and f_wave_features:
            f_wave_score = f_wave_features.get('f_wave_score', f_wave_score)
            f_wave_freq = f_wave_features.get('f_wave_freq', f_wave_freq)
            f_wave_regularity = f_wave_features.get('f_wave_regularity', f_wave_regularity)

        rp_diff = len(r_peaks) - len(p_end_positions)

        quality_score = evaluate_signal_quality_cached(ecg_signal, sampling_rate, r_peaks, lead_type, precomputed_signal_features)

        weights, thresholds = get_adaptive_parameters(quality_score, lead_type)

        rp_diff_norm = thresholds['rp_diff']
        rr_std_norm = thresholds['rr_std']
        rr_cv_norm = thresholds['rr_cv']
        rr_iqr_norm = thresholds['rr_iqr']
        rr_mean_range = thresholds['rr_mean_range']

        scores = calculate_feature_scores(
            rp_diff, rr_std, rr_cv, rr_iqr, rr_mean,
            rp_diff_norm, rr_std_norm, rr_cv_norm, rr_iqr_norm, rr_mean_range,
            f_wave_score, f_wave_freq, f_wave_regularity,
            lead_type, p_wave_enhanced
        )

        if lead_type == 'I' and len(rr_intervals) >= 10:
            enhance_scores_with_autocorrelation(scores, rr_autocorr)

        enhanced_af_score = calculate_total_score(scores, weights)

        final_af_score = enhanced_af_score

        if lead_type == 'I' and DUAL_SCORE_CONFIG.get('enabled', False) and p_wave_enhanced:
            final_af_score = apply_dual_scoring(
                enhanced_af_score, original_rp_diff, rp_diff_norm,
                scores.copy(), weights
            )

        af_threshold = thresholds.get('af_score', THRESHOLDS['af_score'])
        is_af = final_af_score >= af_threshold

        if lead_type == 'I' and not is_af and final_af_score >= af_threshold * 0.92:
            is_af = check_borderline_case(scores)
            if is_af:
                pass

        _internal_computation_cache.clear()

        return bool(is_af)
    except Exception as e:
        _internal_computation_cache.clear()
        return False

def analyze_af_features(waveform_info, lead_type='I', precomputed_signal_features=None):
    """
    分析房颤特征，返回详细的特征指标
    """
    try:
        global _internal_computation_cache
        _internal_computation_cache = {}

        if not waveform_info or 'waveform' not in waveform_info:
            return {}

        waveform = waveform_info['waveform']
        ecg_signal = waveform_info.get('signal', None)
        sampling_rate = waveform_info.get('sampling_rate', 250)

        rr_intervals = waveform.get('rr_intervals', [])
        r_peaks = waveform.get('r_peaks', [])
        p_end_positions = waveform.get('p_end_positions', [])
        t_peaks = waveform.get('t_peaks', [])
        p_start_positions = waveform.get('p_start_positions', [])

        if len(rr_intervals) < 6 or len(r_peaks) < 6:
            return {
                'error': '心搏数不足',
                'rr_count': len(rr_intervals),
                'r_peak_count': len(r_peaks)
            }

        rr_features = analyze_rr_intervals(waveform)
        rr_autocorr = rr_features['rr_autocorr']  # 从RR特征中获取自相关值

        original_rp_diff = len(r_peaks) - len(p_end_positions)

        p_wave_enhanced, p_start_positions, p_end_positions, f_wave_features = enhance_p_wave_detection(
            lead_type, ecg_signal, r_peaks, p_start_positions, p_end_positions,
            t_peaks, sampling_rate
        )

        rp_diff = len(r_peaks) - len(p_end_positions)

        quality_score = evaluate_signal_quality_cached(ecg_signal, sampling_rate, r_peaks, lead_type, precomputed_signal_features)

        weights, thresholds = get_adaptive_parameters(quality_score, lead_type)

        scores = calculate_feature_scores(
            rp_diff, rr_features['std'], rr_features['cv'], rr_features['iqr'], rr_features['mean'],
            thresholds['rp_diff'], thresholds['rr_std'], thresholds['rr_cv'], thresholds['rr_iqr'],
            thresholds['rr_mean_range'],
            f_wave_features.get('f_wave_score', 0) if f_wave_features else 0,
            f_wave_features.get('f_wave_freq', 0) if f_wave_features else 0,
            f_wave_features.get('f_wave_regularity', 1) if f_wave_features else 1,
            lead_type, p_wave_enhanced
        )

        if lead_type == 'I' and len(rr_intervals) >= 10:
            enhance_scores_with_autocorrelation(scores, rr_autocorr)

        enhanced_af_score = calculate_total_score(scores, weights)

        final_af_score = enhanced_af_score
        if lead_type == 'I' and DUAL_SCORE_CONFIG.get('enabled', False) and p_wave_enhanced:
            final_af_score = apply_dual_scoring(
                enhanced_af_score, original_rp_diff, thresholds['rp_diff'],
                scores.copy(), weights
            )

        _internal_computation_cache.clear()

        return {
            'af_score': final_af_score,
            'quality_score': quality_score,
            'rr_features': rr_features,
            'p_wave_features': {
                'original_rp_diff': original_rp_diff,
                'enhanced_rp_diff': rp_diff,
                'p_wave_enhanced': p_wave_enhanced
            },
            'f_wave_features': f_wave_features if f_wave_features else {},
            'scores': scores,
            'weights': weights,
            'thresholds': thresholds,
            'lead_type': lead_type
        }

    except Exception as e:
        _internal_computation_cache.clear()
        return {
            'error': str(e),
            'traceback': traceback.format_exc() if LOG_DETAIL_LEVEL >= 2 else None
        }

def enhance_p_wave_detection(lead_type, ecg_signal, r_peaks, p_start_positions, p_end_positions, t_peaks, sampling_rate):
    """
    使用相量变换增强P波检测
    """
    should_enhance_p_wave = (
        lead_type == 'I' and
        ecg_signal is not None and
        LEAD_SPECIFIC.get(lead_type, {}).get('phasor_enabled', True)
    )

    p_wave_enhanced = False
    f_wave_features = None

    if not should_enhance_p_wave:
        return p_wave_enhanced, p_start_positions, p_end_positions, f_wave_features

    cache_key = f"phasor_{lead_type}_{id(ecg_signal)}"
    if cache_key in _internal_computation_cache:
        cached_data = _internal_computation_cache[cache_key]

        return cached_data

    p_positions, p_end_positions = get_phasor_positions(ecg_signal, r_peaks, sampling_rate, lead_type)

    _internal_computation_cache[cache_key] = (p_positions, p_end_positions)

    if p_positions is None or len(p_positions) == 0 or len(p_end_positions) == 0:
        return p_wave_enhanced, p_start_positions, p_end_positions, f_wave_features

    old_p_count = len(p_end_positions)
    new_p_count = len(p_end_positions)

    use_phasor_results = (
        new_p_count <= old_p_count * 1.5 and
        new_p_count >= old_p_count * 0.6 and
        new_p_count > 0
    )

    if not use_phasor_results:
        return p_wave_enhanced, p_start_positions, p_end_positions, f_wave_features

    p_start_positions = p_positions
    p_wave_enhanced = True
    log_info(f"I导联使用相量变换增强P波检测，发现P波 {new_p_count}个 (原方法: {old_p_count}个)")

    if t_peaks is not None and len(t_peaks) > 0:
        f_wave_features = analyze_tq_segment(ecg_signal, r_peaks, t_peaks, p_start_positions, sampling_rate, lead_type)
        if f_wave_features:
            log_info(f"I导联使用TQ段分析优化F波特征: 分数={f_wave_features['f_wave_score']:.1f}, 频率={f_wave_features['f_wave_freq']:.1f}Hz")

    return p_wave_enhanced, p_start_positions, p_end_positions, f_wave_features

def get_phasor_positions(ecg_signal, r_peaks, sampling_rate, lead_type):
    """获取相量变换的P波位置，使用缓存"""
    try:
        cache_key = f"phasor_{lead_type}_{id(ecg_signal)}"

        if cache_key in _internal_computation_cache:
            cached_data = _internal_computation_cache[cache_key]

            return cached_data

        p_positions, p_end_positions = detect_p_waves_phasor(ecg_signal, r_peaks, sampling_rate)
        _internal_computation_cache[cache_key] = (p_positions, p_end_positions)
        return p_positions, p_end_positions
    except Exception as e:
        log_error("相量变换P波检测失败", e)
        return None, None

def analyze_tq_segment(ecg_signal, r_peaks, t_peaks, p_start_positions, sampling_rate, lead_type):
    """分析TQ段，提取F波特征，使用缓存"""
    try:
        cache_key = f"tq_analysis_{lead_type}_{id(ecg_signal)}"

        if cache_key in _internal_computation_cache:
            cached_data = _internal_computation_cache[cache_key]

            return cached_data

        f_wave_features = tq_segment_analysis(
            ecg_signal, r_peaks, t_peaks,
            p_start_positions,
            sampling_rate
        )
        _internal_computation_cache[cache_key] = f_wave_features
        return f_wave_features
    except Exception as e:
        log_error("TQ段分析失败", e)
        return None

def evaluate_signal_quality_cached(ecg_signal, sampling_rate, r_peaks, lead_type, precomputed_signal_features=None):
    """
    获取（可能已缓存的）信号质量评分
    """
    quality_score = 0.8
    
    if ecg_signal is None or len(r_peaks) == 0:
        return quality_score
        
    try:
        cache_key = f"quality_{lead_type}_{sampling_rate}"
        if cache_key in _internal_computation_cache:
            return _internal_computation_cache[cache_key]
        
        quality_score, _ = evaluate_signal_quality(ecg_signal, sampling_rate, r_peaks, lead_type, precomputed_features=precomputed_signal_features)
        _internal_computation_cache[cache_key] = quality_score
        log_info(f"信号质量评估: {quality_score:.2f}", detail_level=2)
        return quality_score
    except Exception as e:
        log_error("信号质量评估失败", e)
        return quality_score

def get_adaptive_parameters(quality_score, lead_type):
    """获取自适应权重和阈值，包含错误处理"""
    try:
        weights = get_adaptive_weights(quality_score, lead_type)
        thresholds = get_adaptive_thresholds(quality_score, lead_type)
        return weights, thresholds
    except Exception as e:
        log_error("获取自适应参数失败", e)
        return AF_WEIGHTS, THRESHOLDS

def calculate_feature_scores(rp_diff, rr_std, rr_cv, rr_iqr, rr_mean,
                           rp_diff_norm, rr_std_norm, rr_cv_norm, rr_iqr_norm, rr_mean_range,
                           f_wave_score, f_wave_freq, f_wave_regularity,
                           lead_type, p_wave_enhanced):
    """计算各特征归一化得分"""
    scores = {
        'rp_diff': min(1.0, rp_diff / rp_diff_norm),
        'rr_std': min(1.0, rr_std / rr_std_norm),
        'rr_cv': min(1.0, rr_cv / rr_cv_norm),
        'rr_iqr': min(1.0, rr_iqr / rr_iqr_norm),
        'rr_mean': max(0.0, min(1.0, (1.3 - rr_mean) / rr_mean_range))
    }
    
    if lead_type == 'I' and p_wave_enhanced:
        min_rp_score = LEAD_SPECIFIC.get(lead_type, {}).get('rp_diff_min_score', 0.0)
        if min_rp_score > 0:
            scores['rp_diff'] = max(scores['rp_diff'], min_rp_score)
            log_info(f"I导联P波缺失指标使用最低分数限制: {min_rp_score:.2f}")
    
    f_wave_normalized = min(1.0, f_wave_score / 100.0)
    freq_factor = 1.2 if (5 <= f_wave_freq <= 7) else 1.0
    regularity_factor = max(0.5, min(1.5, 1.0 / (f_wave_regularity + 0.01)))
    scores['f_wave'] = min(1.0, f_wave_normalized * freq_factor * regularity_factor)
    
    return scores

def enhance_scores_with_autocorrelation(scores, rr_autocorr):
    """使用自相关分析增强RR间隔相关得分"""
    try:
        af_autocorr_threshold = AUTOCORR_CONFIG.get('af_threshold', 0.4)
        
        if rr_autocorr < af_autocorr_threshold:  # 低自相关提示房颤
            scores['rr_std'] = min(1.0, scores['rr_std'] * 1.15)
            scores['rr_cv'] = min(1.0, scores['rr_cv'] * 1.15)
            
    except Exception as e:
        log_error("自相关增强得分失败", e)

def calculate_total_score(scores, weights):
    """计算总AF评分"""
    total_score = 0.0
    for key in weights:
        total_score += weights[key] * scores[key]
    return total_score

def apply_dual_scoring(enhanced_af_score, original_rp_diff, rp_diff_norm, scores, weights):
    """应用双重评分策略"""
    original_rp_score = min(1.0, original_rp_diff / rp_diff_norm)
    
    scores['rp_diff'] = original_rp_score
    
    legacy_af_score = 0.0
    for key in weights:
        legacy_af_score += weights[key] * scores[key]
    
    legacy_weight = DUAL_SCORE_CONFIG.get('legacy_weight', 0.5)
    enhanced_weight = DUAL_SCORE_CONFIG.get('enhanced_weight', 0.5)
    minimal_legacy_ratio = DUAL_SCORE_CONFIG.get('minimal_legacy_score', 0.6)
    

    final_af_score = enhanced_af_score
    if legacy_af_score > enhanced_af_score * minimal_legacy_ratio:
        final_af_score = legacy_af_score * legacy_weight + enhanced_af_score * enhanced_weight
    else:
        final_af_score = max(legacy_af_score, enhanced_af_score)
        
    log_info(f"双重评分: 原始={legacy_af_score:.3f}, 增强={enhanced_af_score:.3f}, 最终={final_af_score:.3f}")
    return final_af_score

def check_borderline_case(scores):
    """检查边缘情况是否符合房颤特征"""
    key_indicators_strong = (
        scores['rr_std'] > 0.8 and  
        scores['rr_cv'] > 0.7 and  
        (scores['rp_diff'] > 0.7 or scores['f_wave'] > 0.7)  
    )
    return key_indicators_strong


version: "3"
services:
  ecg-analysis:
    build:
      context: .
    container_name: ecg-analysis-dev
    ports:
      - "7209:7209"
    volumes:
      - /mnt/servers/ECGAnalysisDev/logs:/app/logs # 将宿主机的 logs 目录挂载到容器的 /app/logs 目录
    expose:
      - "7209"
    command: python manage.py runserver 0.0.0.0:7209
    restart: always
    environment:
      TZ: Asia/Shanghai
    networks:
      - ecg_network

networks:
  ecg_network:
    external:
      name: ecg_network
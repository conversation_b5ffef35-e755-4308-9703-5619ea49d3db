import traceback
from apps.analysis.arrhythmia_diagnosis import sna, af, snt, snb, pvc, pac
from apps.analysis.common import ecg_llm_processing, tf_model_predict
from apps.analysis.diagnosis_filter.filter import apply_rules
from apps.analysis.common.ecg_signal_processing import resample
from apps.models.analysis_models import ArrhythmiaDiagnosisEntity
from apps.utils.logger_helper import Logger
from global_settings import mutually_exclusive_conditions, conclusion_diagnostic_dict


def process(ecg_data, sampling_rate, waveform_info, precomputed_signal_features=None):
    """
    心率失常分析
    :param ecg_data: ECG信号数据 (原始)
    :param sampling_rate: 采样率
    :param waveform_info: 波形信息 (来自 get_waveform，基于原始信号)
    :param precomputed_signal_features: 从 available.py 传递过来的预计算特征字典 (可选)
    :return: 心率失常分析结果
    """

    arrhythmia_diagnosis_entity = ArrhythmiaDiagnosisEntity()

    # --- 方法诊断 - 传入原始信号 ---
    method_diag_dict = method_diagnosis(ecg_data, sampling_rate, waveform_info, precomputed_signal_features)

    if any(method_diag_dict.values()):
        diagnostic_results = [key for key, value in method_diag_dict.items() if value]
    else:
        # --- 多结论模型诊断 - 传入原始信号 ---
        conclusions = tf_diagnosis(ecg_data, sampling_rate)

        if len(conclusions) == 0:
            # 大模型结论分析
            diagnostic_results = llm_diagnosis(waveform_info, sampling_rate)
        else:
            diagnostic_results = conclusions

    # 症状互斥处理
    if diagnostic_results:
        final_labels = apply_mutually_exclusive_rules(diagnostic_results)

        if len(final_labels) == 0:
            arrhythmia_diagnosis_entity.SN = 1
        else:
            for final_label in final_labels:
                setattr(arrhythmia_diagnosis_entity, final_label, 1)
    else:
        arrhythmia_diagnosis_entity.SN = 1

    return arrhythmia_diagnosis_entity


def method_diagnosis(ecg_data, sampling_rate, waveform_info, precomputed_signal_features=None):
    """
    方法诊断
    :param ecg_data: 原始 ECG 信号数据
    :param sampling_rate: 采样率
    :param waveform_info: *原始信号*计算出的波形信息 (来自 get_waveform)
    :param precomputed_signal_features: 预计算的信号特征 (可选)
    :return:
    """
    rr_cv_original = waveform_info['waveform']['rr_cv']
    hr = waveform_info['heart_rate']['hr']

    diag_dict = {
        'SNA': False,
        'SNT': False,
        'SNB': False,
        'AF': False,
        'SN': False,
        'PVC': False,
        'PAC': False
    }

    try:
        # 定义诊断模块及其对应的键
        diagnosis_modules = {
            'AF': af.detect_atrial_fibrillation,
            'SNT': snt.process,
            'SNB': snb.process,
            'PVC': pvc.process,
            'PAC': pac.process,
            'SNA': sna.process
        }

        # 步骤1: 优先诊断房颤 (AF)
        diag_dict['AF'] = af.detect_atrial_fibrillation(waveform_info, precomputed_signal_features=precomputed_signal_features)

        # 步骤2: 仅在没有房颤的情况下，诊断其他心律失常
        # 遍历模块并更新诊断结果
        for key, module in diagnosis_modules.items():
            # PVC和PAC的诊断逻辑分开处理
            if key == 'PVC':  # PVC诊断不受房颤影响
                if waveform_info.get('waveform'):
                    diag_dict[key] = module(ecg_data, sampling_rate, waveform_info)
                else:
                    Logger().warning(f"{key} 诊断跳过，因为 waveform_features 不可用")
                    diag_dict[key] = False
            elif key == 'PAC':  # PAC诊断仍然需要考虑房颤的影响
                if not diag_dict['AF']:  # 如果不是房颤，才进行PAC诊断
                    if waveform_info.get('waveform'):
                        diag_dict[key] = module(ecg_data, sampling_rate, waveform_info)
                    else:
                        Logger().warning(f"{key} 诊断跳过，因为 waveform_features 不可用")
                        diag_dict[key] = False
                else:
                    diag_dict[key] = False
            else:  # SNT, SNB, SNA 的诊断照常进行
                diag_dict[key] = module(waveform_info)

        if not any(diag_dict[key] for key in diagnosis_modules) and not diag_dict['AF']:
            sn_condition = 0.05 < rr_cv_original < 0.8
            diag_dict['SN'] = sn_condition
        else:
            pass

    except Exception as e:
        Logger().error(f'方法诊断异常：{traceback.format_exc()}') # 保留 Error
    finally:
        pass # 添加 pass
        return diag_dict


def tf_diagnosis(ecg_data, sampling_rate):
    """
    tensorflow多结论模型诊断
    :param ecg_data: 原始 ECG 信号数据
    :param sampling_rate: 采样率
    :return:
    """
    ecg_data = resample(ecg_data, sampling_rate)  # 重采样

    # 模型预测
    model_name = "diagnosis"
    predictions = tf_model_predict.process(ecg_data.reshape(1, len(ecg_data), 1), model_name)

    if predictions is None:
        return []
    else:
        predict = predictions['predictions']

    # 动态阈值参数
    initial_threshold = 0.9  # 起始阈值
    min_threshold = 0.5  # 最低阈值
    step = 0.1  # 每次下降的步长

    # 根据字典顺序获得结果对应的标签
    values = list(conclusion_diagnostic_dict.values())

    # 初始化输出
    predicted_labels = []

    # 动态调整阈值以找到符合条件的标签
    threshold = initial_threshold
    while threshold >= min_threshold:
        predicted_labels = [values[i] for i, score in enumerate(predict[0]) if score >= threshold]
        # 如果找到了符合条件的标签，则停止降低阈值
        if predicted_labels:
            break
        # 否则继续降低阈值
        threshold -= step

    return predicted_labels


def llm_diagnosis(waveform_info, sampling_rate):
    """
    大模型诊断
    :param waveform_info: 波形信息
    :param sampling_rate: 采样率
    :return:
    """
    try:
        waveform = waveform_info['waveform']
        hr = waveform_info['heart_rate']['hr']
        rr_intervals = waveform['rr_intervals']
        rr_cv = waveform['rr_cv']
        rr_std = waveform['rr_std']
        rr_iqr = waveform['rr_iqr']
        rr_average = waveform['rr_average']
        qrs_average = waveform['qrs_average']

        first_diag = ['正常心电图', '窦速', '窦缓', '窦性心律不齐', '房颤', '室性早搏', '噪音']
        diag_zh = {'SNA': '窦性心律不齐', 'SNT': '窦速', 'SNB': '窦缓', 'AF': '房颤', 'SN': '正常心电图', 'PVC': '室性早搏'}
        diag_llm_dict = {'SNA': False, 'SNT': False, 'SNB': False, 'AF': False, 'SN': False, 'PVC': False}

        diag_base_list = []

        prompt_intext = '当前心电图数据特征为心率为{}次/min,RR间期数量{}个,RR间期的平均时长为{}ms,RR间期的标准差为{},RR间期变异系数为{},RR间期的IQR为{}，qrs间期时长均值为{}秒。'.format(
            hr, len(rr_intervals), rr_average, rr_std, rr_cv, rr_iqr, qrs_average)
        llm_list = ecg_llm_processing.llm_api(prompt_intext, sampling_rate)

        if isinstance(llm_list, list):
            llm_pred = list(set(first_diag) & set(llm_list))
            if "正常心电图" in llm_pred:
                diag_llm_dict['SN'] = True
                return diag_llm_dict
            else:
                if '房颤' not in diag_base_list and '房颤' in llm_list:
                    diag_base_list.append('房颤')
                if '室性早搏' not in diag_base_list and '室性早搏' in llm_list:
                    diag_base_list.append('室性早搏')

                for key, value in diag_zh.items():
                    if value in diag_base_list:
                        diag_llm_dict[key] = True
                return diag_llm_dict

        return None
    except Exception:
        Logger().error(f'大模型诊断异常：{traceback.format_exc()}')
        return None


def apply_mutually_exclusive_rules(predicted_labels):
    """
    根据互斥规则过滤预测标签
    :param predicted_labels: 模型预测的疾病标签列表
    :return: 经过互斥规则过滤后的疾病标签列表
    """
    final_labels = []

    for label in predicted_labels:
        if label in final_labels:
            continue

        final_labels.append(label)

        if label in mutually_exclusive_conditions:
            mutually_exclusive = mutually_exclusive_conditions[label]
            predicted_labels = [lbl for lbl in predicted_labels if lbl not in mutually_exclusive]

    return final_labels
